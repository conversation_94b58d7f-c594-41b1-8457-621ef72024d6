import pandas as pd
import folium
import matplotlib.pyplot as plt
import numpy as np
import warnings
import os
warnings.filterwarnings('ignore')

# --- 字体设置 ---
def setup_chinese_font():
    """设置中文字体"""
    fonts = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial Unicode MS']
    for font in fonts:
        try:
            plt.rcParams['font.sans-serif'] = [font]
            plt.rcParams['axes.unicode_minus'] = False
            print(f"使用字体: {font}")
            break
        except:
            continue

# --- 机场地理坐标数据 ---
airport_coordinates = {
    'ZWHM': {'name': '武汉天河', 'lat': 30.7837, 'lon': 114.2081},
    'ZBDH': {'name': '秦皇岛北戴河', 'lat': 39.6683, 'lon': 119.0581},
    'ZBLA': {'name': '呼伦贝尔', 'lat': 49.2050, 'lon': 119.8250},
    'ZSHC': {'name': '杭州萧山', 'lat': 30.2295, 'lon': 120.4348},
    'ZPLJ': {'name': '丽江三义', 'lat': 26.6800, 'lon': 100.2467},
    'ZBHH': {'name': '呼和浩特', 'lat': 40.8517, 'lon': 111.8242},
    'ZSSS': {'name': '上海虹桥', 'lat': 31.1979, 'lon': 121.3364},
    'ZBAD': {'name': '北京大兴', 'lat': 39.5098, 'lon': 116.4104},
    'ZWSH': {'name': '喀什徕宁', 'lat': 39.5428, 'lon': 76.0200},
    'ZHCC': {'name': '海口美兰', 'lat': 19.9349, 'lon': 110.4591},
    'ZJHK': {'name': '西双版纳', 'lat': 21.9737, 'lon': 101.1337},
    'ZSJN': {'name': '济南遥墙', 'lat': 36.8572, 'lon': 117.2156},
    'ZWFY': {'name': '富蕴可可托海', 'lat': 46.8044, 'lon': 89.5117},
    'ZYDQ': {'name': '大庆萨尔图', 'lat': 46.3733, 'lon': 124.8758},
    'ZWTL': {'name': '吐鲁番交河', 'lat': 42.8067, 'lon': 89.0983},
    'ZWQT': {'name': '奇台江布拉克', 'lat': 44.0244, 'lon': 89.3717},
    'ZWYT': {'name': '伊宁', 'lat': 43.9556, 'lon': 81.3303},
    'ZWYN': {'name': '莎车叶尔羌', 'lat': 38.1383, 'lon': 77.7242},
    'ZYMH': {'name': '漠河古莲', 'lat': 52.9311, 'lon': 122.4289}
}

# --- 原始数据 ---
risk_data = {
    '空客': [
        ('ZWHM', '非精密进近', 1), ('ZBDH', '非精密进近', 2), ('ZBLA', '非精密进近', 3),
        ('ZSHC', '高进近', 1), ('ZPLJ', '高进近', 2), ('ZBHH', '高进近', 3),
        ('ZSSS', '平均载荷', 1), ('ZBAD', '平均载荷', 2), ('ZWSH', '平均载荷', 3),
        ('ZHCC', '大侧风/乱流', 1), ('ZJHK', '大侧风/乱流', 2), ('ZSJN', '大侧风/乱流', 3),
    ],
    'C909': [
        ('ZWFY', '非精密进近', 1), ('ZWSH', '非精密进近', 2), ('ZYDQ', '非精密进近', 3),
        ('ZWTL', '高进近', 1), ('ZWSH', '高进近', 2), ('ZWQT', '高进近', 3),
        ('ZWYT', '平均载荷', 1), ('ZYDQ', '平均载荷', 2), ('ZWFY', '平均载荷', 3),
        ('ZWTL', '大侧风/乱流', 1), ('ZWYN', '大侧风/乱流', 2), ('ZYMH', '大侧风/乱流', 3),
    ]
}

points_map = {1: 3, 2: 2, 3: 1}

def process_airport_data(aircraft_type):
    """处理机场数据，计算风险得分并添加地理坐标"""
    df_data = []
    for code, risk_item, rank in risk_data[aircraft_type]:
        score = points_map[rank]
        airport_info = airport_coordinates.get(code, {})
        airport_name = airport_info.get('name', code)
        lat = airport_info.get('lat', 0)
        lon = airport_info.get('lon', 0)
        
        df_data.append({
            'ICAO': code,
            '机场': airport_name,
            '风险维度': risk_item,
            '风险分': score,
            '纬度': lat,
            '经度': lon
        })
    
    df = pd.DataFrame(df_data)
    
    # 计算每个机场的总风险分
    airport_total_scores = df.groupby(['ICAO', '机场', '纬度', '经度'])['风险分'].sum().reset_index()
    airport_total_scores = airport_total_scores.rename(columns={'风险分': '总风险分'})
    
    # 获取风险详情
    risk_details = df.groupby(['ICAO', '机场']).apply(
        lambda x: '; '.join([f"{row['风险维度']}({row['风险分']}分)" for _, row in x.iterrows()])
    ).reset_index()
    risk_details = risk_details.rename(columns={0: '风险详情'})
    
    # 合并数据
    result = pd.merge(airport_total_scores, risk_details, on=['ICAO', '机场'])
    
    return result

def create_risk_map(aircraft_type, save_html=True):
    """创建风险地图"""
    # 处理数据
    df = process_airport_data(aircraft_type)
    
    # 创建地图，使用高德地图作为底图
    # 中心点设置为中国中心
    m = folium.Map(
        location=[35.0, 105.0],  # 中国中心坐标
        zoom_start=5,
        tiles=None  # 不使用默认底图
    )
    
    # 添加高德地图底图
    folium.TileLayer(
        tiles='https://webrd02.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=7&x={x}&y={y}&z={z}',
        attr='高德地图',
        name='高德地图',
        overlay=False,
        control=True
    ).add_to(m)
    
    # 定义颜色映射（根据风险分数）
    def get_color_and_size(score):
        if score >= 6:
            return '#d73027', 15  # 红色，大圆点
        elif score >= 4:
            return '#fc8d59', 12  # 橙色，中圆点
        elif score >= 2:
            return '#fee08b', 10  # 黄色，中圆点
        else:
            return '#91cf60', 8   # 绿色，小圆点
    
    # 添加机场标记
    for _, row in df.iterrows():
        if row['纬度'] != 0 and row['经度'] != 0:  # 确保坐标有效
            color, size = get_color_and_size(row['总风险分'])
            
            # 创建弹出框内容
            popup_html = f"""
            <div style="width: 300px; font-family: 'Microsoft YaHei', sans-serif;">
                <h4 style="margin-bottom: 10px; color: #333;">
                    🛫 {row['机场']} ({row['ICAO']})
                </h4>
                <hr style="margin: 5px 0;">
                <p><strong>📊 总风险分:</strong> 
                   <span style="color: {color}; font-weight: bold; font-size: 16px;">
                       {row['总风险分']}分
                   </span>
                </p>
                <p><strong>📋 风险详情:</strong><br>
                   {row['风险详情']}
                </p>
                <p><strong>🎯 机型:</strong> {aircraft_type}</p>
                <p><strong>📍 坐标:</strong> {row['纬度']:.3f}, {row['经度']:.3f}</p>
            </div>
            """
            
            # 添加圆形标记
            folium.CircleMarker(
                location=[row['纬度'], row['经度']],
                radius=size,
                color='white',
                weight=2,
                fillColor=color,
                fillOpacity=0.8,
                popup=folium.Popup(popup_html, max_width=350),
                tooltip=f"{row['机场']} (总分: {row['总风险分']})"
            ).add_to(m)
            
            # 添加文字标签
            folium.Marker(
                location=[row['纬度'], row['经度']],
                icon=folium.DivIcon(
                    html=f'<div style="font-size: 10px; color: black; font-weight: bold; '
                         f'text-shadow: 1px 1px 1px white; margin-left: 20px; margin-top: -5px;">'
                         f'{row["机场"]}</div>',
                    icon_size=(1, 1),
                    icon_anchor=(0, 0)
                )
            ).add_to(m)
    
    # 添加图例
    legend_html = f'''
    <div style="position: fixed; 
                top: 10px; right: 10px; width: 200px; height: 180px; 
                background-color: white; border:2px solid grey; z-index:9999; 
                font-size:14px; font-family: 'Microsoft YaHei', sans-serif;
                padding: 10px; box-shadow: 2px 2px 6px rgba(0,0,0,0.3);">
    <h4 style="margin-top: 0; text-align: center; color: #333;">
        {aircraft_type} 风险等级
    </h4>
    <p style="margin: 8px 0;">
        <span style="color: #d73027;">●</span> 高风险 (6+ 分)
    </p>
    <p style="margin: 8px 0;">
        <span style="color: #fc8d59;">●</span> 中高风险 (4-5 分)
    </p>
    <p style="margin: 8px 0;">
        <span style="color: #fee08b;">●</span> 中风险 (2-3 分)
    </p>
    <p style="margin: 8px 0;">
        <span style="color: #91cf60;">●</span> 低风险 (1 分)
    </p>
    <p style="margin: 8px 0; font-size: 12px; color: #666;">
        点击标记查看详情
    </p>
    </div>
    '''
    m.get_root().html.add_child(folium.Element(legend_html))
    
    # 保存地图
    if save_html:
        filename = f'{aircraft_type}_airport_risk_map.html'
        m.save(filename)
        print(f"地图已保存为: {filename}")
    
    return m

def create_static_risk_map(aircraft_type, save_png=True):
    """创建静态风险地图（使用matplotlib）"""
    setup_chinese_font()

    # 处理数据
    df = process_airport_data(aircraft_type)

    # 创建图形
    fig, ax = plt.subplots(figsize=(16, 12))

    # 设置中国地图的大致边界
    china_bounds = {
        'lon_min': 73, 'lon_max': 135,
        'lat_min': 18, 'lat_max': 54
    }

    # 设置地图范围
    ax.set_xlim(china_bounds['lon_min'], china_bounds['lon_max'])
    ax.set_ylim(china_bounds['lat_min'], china_bounds['lat_max'])

    # 定义颜色映射（根据风险分数）
    def get_color_and_size(score):
        if score >= 6:
            return '#d73027', 200  # 红色，大圆点
        elif score >= 4:
            return '#fc8d59', 150  # 橙色，中圆点
        elif score >= 2:
            return '#fee08b', 100  # 黄色，中圆点
        else:
            return '#91cf60', 80   # 绿色，小圆点

    # 绘制机场标记
    for _, row in df.iterrows():
        if row['纬度'] != 0 and row['经度'] != 0:  # 确保坐标有效
            color, size = get_color_and_size(row['总风险分'])

            # 绘制圆点
            ax.scatter(row['经度'], row['纬度'],
                      c=color, s=size, alpha=0.8,
                      edgecolors='black', linewidth=1,
                      zorder=5)

            # 添加机场名称标签
            ax.annotate(f"{row['机场']}\n({row['总风险分']}分)",
                       (row['经度'], row['纬度']),
                       xytext=(5, 5), textcoords='offset points',
                       fontsize=8, ha='left', va='bottom',
                       bbox=dict(boxstyle='round,pad=0.3',
                               facecolor='white', alpha=0.7),
                       zorder=6)

    # 设置标题和标签
    ax.set_title(f'{aircraft_type} 机型机场风险分布地图', fontsize=16, pad=20)
    ax.set_xlabel('经度', fontsize=12)
    ax.set_ylabel('纬度', fontsize=12)

    # 添加网格
    ax.grid(True, alpha=0.3)

    # 创建图例
    legend_elements = [
        plt.scatter([], [], c='#d73027', s=200, alpha=0.8, edgecolors='black', label='高风险 (6+ 分)'),
        plt.scatter([], [], c='#fc8d59', s=150, alpha=0.8, edgecolors='black', label='中高风险 (4-5 分)'),
        plt.scatter([], [], c='#fee08b', s=100, alpha=0.8, edgecolors='black', label='中风险 (2-3 分)'),
        plt.scatter([], [], c='#91cf60', s=80, alpha=0.8, edgecolors='black', label='低风险 (1 分)')
    ]

    ax.legend(handles=legend_elements, loc='upper right',
             bbox_to_anchor=(0.98, 0.98), fontsize=10)

    # 设置背景色
    ax.set_facecolor('#f0f8ff')  # 淡蓝色背景

    # 调整布局
    plt.tight_layout()

    # 保存静态地图
    if save_png:
        filename = f'{aircraft_type}_static_risk_map.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        print(f"静态地图已保存为: {filename}")

    plt.show()
    return fig

def create_risk_statistics_chart(aircraft_type):
    """创建风险统计图表"""
    setup_chinese_font()
    df = process_airport_data(aircraft_type)
    
    # 创建子图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(f'{aircraft_type} 机型机场风险分析统计', fontsize=16, y=0.98)
    
    # 1. 风险分布直方图
    ax1.hist(df['总风险分'], bins=range(1, df['总风险分'].max()+2), 
             alpha=0.7, color='skyblue', edgecolor='black')
    ax1.set_title('机场风险分数分布', fontsize=12)
    ax1.set_xlabel('风险分数')
    ax1.set_ylabel('机场数量')
    ax1.grid(True, alpha=0.3)
    
    # 2. 排名前10的高风险机场
    top_airports = df.nlargest(10, '总风险分')
    ax2.barh(range(len(top_airports)), top_airports['总风险分'],
             color=['#d73027' if x >= 6 else '#fc8d59' if x >= 4 else '#fee08b'
                    for x in top_airports['总风险分']])
    ax2.set_yticks(range(len(top_airports)))
    ax2.set_yticklabels(top_airports['机场'], fontsize=10)
    ax2.set_title('风险评分Top 10机场', fontsize=12)
    ax2.set_xlabel('风险分数')
    ax2.invert_yaxis()
    
    # 添加数值标签
    for i, v in enumerate(top_airports['总风险分']):
        ax2.text(v + 0.1, i, str(int(v)), va='center', fontsize=9)
    
    # 3. 地理分布散点图
    colors = ['#d73027' if x >= 6 else '#fc8d59' if x >= 4 else '#fee08b' if x >= 2 else '#91cf60'
              for x in df['总风险分']]
    sizes = [x * 30 for x in df['总风险分']]

    ax3.scatter(df['经度'], df['纬度'], c=colors, s=sizes, alpha=0.6, edgecolors='black')
    ax3.set_title('机场地理位置与风险分布', fontsize=12)
    ax3.set_xlabel('经度')
    ax3.set_ylabel('纬度')
    ax3.grid(True, alpha=0.3)
    
    # 添加机场名称标签（只显示高风险机场）
    for _, row in df.iterrows():
        if row['总风险分'] >= 4:  # 只标注高风险机场
            ax3.annotate(row['机场'], (row['经度'], row['纬度']), 
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
    
    # 4. 风险等级饼图
    risk_levels = []
    risk_labels = []
    colors_pie = []
    
    high_risk = len(df[df['总风险分'] >= 6])
    medium_high = len(df[(df['总风险分'] >= 4) & (df['总风险分'] < 6)])
    medium_risk = len(df[(df['总风险分'] >= 2) & (df['总风险分'] < 4)])
    low_risk = len(df[df['总风险分'] < 2])
    
    if high_risk > 0:
        risk_levels.append(high_risk)
        risk_labels.append(f'高风险 ({high_risk}个)')
        colors_pie.append('#d73027')
    
    if medium_high > 0:
        risk_levels.append(medium_high)
        risk_labels.append(f'中高风险 ({medium_high}个)')
        colors_pie.append('#fc8d59')
    
    if medium_risk > 0:
        risk_levels.append(medium_risk)
        risk_labels.append(f'中风险 ({medium_risk}个)')
        colors_pie.append('#fee08b')
    
    if low_risk > 0:
        risk_levels.append(low_risk)
        risk_labels.append(f'低风险 ({low_risk}个)')
        colors_pie.append('#91cf60')
    
    ax4.pie(risk_levels, labels=risk_labels, colors=colors_pie, autopct='%1.1f%%', startangle=90)
    ax4.set_title('风险等级占比', fontsize=12)
    
    plt.tight_layout()
    plt.savefig(f'{aircraft_type}_risk_statistics.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_risk_report(aircraft_type):
    """生成风险评估报告"""
    df = process_airport_data(aircraft_type)
    
    report = f"""
    ═══════════════════════════════════════
    {aircraft_type} 机型机场技术风险评估报告
    ═══════════════════════════════════════
    
    📊 总体统计:
    • 评估机场总数: {len(df)} 个
    • 平均风险分: {df['总风险分'].mean():.2f} 分
    • 最高风险分: {df['总风险分'].max()} 分
    • 最低风险分: {df['总风险分'].min()} 分
    
    🚨 高风险机场 (≥6分):
    """
    
    high_risk_airports = df[df['总风险分'] >= 6].sort_values('总风险分', ascending=False)
    if len(high_risk_airports) > 0:
        for _, row in high_risk_airports.iterrows():
            report += f"    • {row['机场']} ({row['ICAO']}): {row['总风险分']}分\n"
            report += f"      风险详情: {row['风险详情']}\n"
    else:
        report += "    无高风险机场\n"
    
    report += f"""
    ⚠️  中高风险机场 (4-5分):
    """
    
    medium_high_airports = df[(df['总风险分'] >= 4) & (df['总风险分'] < 6)].sort_values('总风险分', ascending=False)
    if len(medium_high_airports) > 0:
        for _, row in medium_high_airports.iterrows():
            report += f"    • {row['机场']} ({row['ICAO']}): {row['总风险分']}分\n"
    else:
        report += "    无中高风险机场\n"
    
    report += f"""
    📈 风险分布:
    • 高风险 (≥6分): {len(df[df['总风险分'] >= 6])} 个机场
    • 中高风险 (4-5分): {len(df[(df['总风险分'] >= 4) & (df['总风险分'] < 6)])} 个机场  
    • 中风险 (2-3分): {len(df[(df['总风险分'] >= 2) & (df['总风险分'] < 4)])} 个机场
    • 低风险 (<2分): {len(df[df['总风险分'] < 2])} 个机场
    
    🎯 建议关注重点:
    """
    
    # 按风险分数排序，给出前5个需要重点关注的机场
    top_concern = df.nlargest(5, '总风险分')
    for i, (_, row) in enumerate(top_concern.iterrows(), 1):
        report += f"    {i}. {row['机场']}: {row['总风险分']}分 - 重点关注{row['风险详情']}\n"
    
    report += "\n" + "="*50
    
    # 保存报告
    with open(f'{aircraft_type}_risk_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(report)
    return report

def main():
    """主程序"""
    print("🛫 机场技术风险地图可视化系统启动...")
    
    aircraft_types = ['空客', 'C909']
    
    for aircraft_type in aircraft_types:
        print(f"\n{'='*50}")
        print(f"处理 {aircraft_type} 机型数据...")
        print(f"{'='*50}")
        
        try:
            # 1. 创建交互式风险地图
            print("📍 生成交互式风险地图...")
            create_risk_map(aircraft_type)

            # 2. 创建静态风险地图
            print("🗺️ 生成静态风险地图...")
            create_static_risk_map(aircraft_type)

            # 3. 创建统计图表
            print("📊 生成统计图表...")
            create_risk_statistics_chart(aircraft_type)

            # 4. 生成风险报告
            print("📋 生成风险评估报告...")
            generate_risk_report(aircraft_type)
            
            print(f"✅ {aircraft_type} 机型分析完成!")
            
        except Exception as e:
            print(f"❌ 处理 {aircraft_type} 时出错: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🎉 所有分析完成！生成文件包括:")
    print("   • HTML交互地图文件")
    print("   • PNG静态地图文件")
    print("   • PNG统计图表文件")
    print("   • TXT风险评估报告")

# 额外功能：比较两种机型的风险分布
def compare_aircraft_risks():
    """比较两种机型的风险分布"""
    setup_chinese_font()
    
    # 获取两种机型数据
    df_airbus = process_airport_data('空客')
    df_c909 = process_airport_data('C909')
    
    # 创建比较图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    fig.suptitle('空客 vs C909 机型风险对比分析', fontsize=16)
    
    # 1. 风险分布对比
    ax1.hist([df_airbus['总风险分'], df_c909['总风险分']], 
             bins=range(1, max(df_airbus['总风险分'].max(), df_c909['总风险分'].max())+2),
             label=['空客', 'C909'], alpha=0.7, color=['skyblue', 'lightcoral'])
    ax1.set_title('风险分数分布对比')
    ax1.set_xlabel('风险分数')
    ax1.set_ylabel('机场数量')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 平均风险对比
    avg_risks = [df_airbus['总风险分'].mean(), df_c909['总风险分'].mean()]
    max_risks = [df_airbus['总风险分'].max(), df_c909['总风险分'].max()]
    
    x = ['空客', 'C909']
    width = 0.35
    x_pos = np.arange(len(x))
    
    ax2.bar(x_pos - width/2, avg_risks, width, label='平均风险分', color='skyblue', alpha=0.8)
    ax2.bar(x_pos + width/2, max_risks, width, label='最高风险分', color='lightcoral', alpha=0.8)
    
    ax2.set_title('机型风险指标对比')
    ax2.set_ylabel('风险分数')
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels(x)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, v in enumerate(avg_risks):
        ax2.text(i - width/2, v + 0.1, f'{v:.2f}', ha='center', va='bottom')
    for i, v in enumerate(max_risks):
        ax2.text(i + width/2, v + 0.1, f'{v}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('aircraft_risk_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 输出对比总结
    print(f"""
    ═══════════════════════════════════
    机型风险对比总结
    ═══════════════════════════════════
    空客机型:
    • 评估机场数: {len(df_airbus)}
    • 平均风险分: {df_airbus['总风险分'].mean():.2f}
    • 最高风险分: {df_airbus['总风险分'].max()}
    • 高风险机场数: {len(df_airbus[df_airbus['总风险分'] >= 6])}
    
    C909机型:
    • 评估机场数: {len(df_c909)}
    • 平均风险分: {df_c909['总风险分'].mean():.2f}
    • 最高风险分: {df_c909['总风险分'].max()}
    • 高风险机场数: {len(df_c909[df_c909['总风险分'] >= 6])}
    """)

if __name__ == "__main__":
    # 运行主程序
    main()
    
    # 运行机型对比分析
    print("\n" + "="*60)
    print("开始机型对比分析...")
    compare_aircraft_risks()

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/maps/china.js"></script>

    
</head>
<body >
    <div id="1c619f32d4c64c7b9b901591100c27a7" class="chart-container" style="width:100vw; height:100vh; "></div>
    <script>
        var chart_1c619f32d4c64c7b9b901591100c27a7 = echarts.init(
            document.getElementById('1c619f32d4c64c7b9b901591100c27a7'), 'white', {renderer: 'canvas'});
        var option_1c619f32d4c64c7b9b901591100c27a7 = {
    "backgroundColor": "#f8f9fa",
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "effectScatter",
            "name": "\u673a\u573a\u98ce\u9669\u6307\u6570",
            "coordinateSystem": "geo",
            "showEffectOn": "render",
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            },
            "symbolSize": 15,
            "data": [
                {
                    "name": "\u6b66\u6c49\u5929\u6cb3",
                    "value": [
                        114.207,
                        30.776,
                        3
                    ]
                },
                {
                    "name": "\u79e6\u7687\u5c9b\u5317\u6234\u6cb3",
                    "value": [
                        119.516,
                        39.795,
                        2
                    ]
                },
                {
                    "name": "\u547c\u4f26\u8d1d\u5c14\u6d77\u62c9\u5c14",
                    "value": [
                        119.845,
                        49.205,
                        1
                    ]
                },
                {
                    "name": "\u676d\u5dde\u8427\u5c71",
                    "value": [
                        120.434,
                        30.229,
                        3
                    ]
                },
                {
                    "name": "\u4e3d\u6c5f\u4e09\u4e49",
                    "value": [
                        100.246,
                        26.683,
                        2
                    ]
                },
                {
                    "name": "\u547c\u548c\u6d69\u7279\u767d\u5854",
                    "value": [
                        111.821,
                        40.852,
                        1
                    ]
                },
                {
                    "name": "\u4e0a\u6d77\u8679\u6865",
                    "value": [
                        121.336,
                        31.198,
                        3
                    ]
                },
                {
                    "name": "\u5317\u4eac\u5927\u5174",
                    "value": [
                        116.41,
                        39.509,
                        2
                    ]
                },
                {
                    "name": "\u5580\u4ec0\u5f95\u5b81",
                    "value": [
                        76.022,
                        39.543,
                        1
                    ]
                },
                {
                    "name": "\u6d77\u53e3\u7f8e\u5170",
                    "value": [
                        110.459,
                        19.939,
                        3
                    ]
                },
                {
                    "name": "\u897f\u53cc\u7248\u7eb3\u560e\u6d12",
                    "value": [
                        100.762,
                        21.974,
                        2
                    ]
                },
                {
                    "name": "\u6d4e\u5357\u9065\u5899",
                    "value": [
                        117.216,
                        36.857,
                        1
                    ]
                }
            ],
            "label": {
                "show": true,
                "position": "right",
                "color": "#333",
                "margin": 8,
                "fontSize": 10,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u673a\u573a\u98ce\u9669\u6307\u6570"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "formatter": "{b}<br/>\u98ce\u9669\u6307\u6570: {c}",
        "textStyle": {
            "fontSize": 12
        },
        "backgroundColor": "rgba(255,255,255,0.9)",
        "borderColor": "#34495e",
        "borderWidth": 1,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "\u7a7a\u5ba2 \u673a\u578b\u6280\u672f\u96be\u5ea6\u673a\u573a\u5206\u5e03\u56fe",
            "target": "blank",
            "subtext": "\u98ce\u9669\u6307\u6570\u8d8a\u9ad8\uff0c\u5706\u70b9\u8d8a\u5927\u989c\u8272\u8d8a\u6df1\uff0c\u9700\u91cd\u70b9\u5173\u6ce8",
            "subtarget": "blank",
            "left": "center",
            "top": "2%",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false,
            "textStyle": {
                "color": "#2c3e50",
                "fontWeight": "bold",
                "fontSize": 24
            },
            "subtextStyle": {
                "color": "#7f8c8d",
                "fontSize": 14
            }
        }
    ],
    "toolbox": {
        "show": true,
        "orient": "horizontal",
        "itemSize": 15,
        "itemGap": 10,
        "left": "2%",
        "top": "2%",
        "feature": {
            "saveAsImage": {
                "title": "\u4fdd\u5b58\u56fe\u7247"
            },
            "restore": {
                "title": "\u91cd\u7f6e"
            },
            "dataZoom": {
                "title": "\u7f29\u653e"
            }
        }
    },
    "visualMap": {
        "show": true,
        "type": "continuous",
        "min": 1,
        "max": 3,
        "inRange": {
            "color": [
                "#2ecc71",
                "#f39c12",
                "#e74c3c"
            ]
        },
        "calculable": true,
        "inverse": false,
        "splitNumber": 5,
        "hoverLink": true,
        "orient": "vertical",
        "top": "20%",
        "right": "2%",
        "padding": 5,
        "showLabel": true,
        "itemWidth": 20,
        "itemHeight": 140,
        "borderWidth": 0
    },
    "geo": {
        "map": "china",
        "roam": true,
        "aspectScale": 0.75,
        "nameProperty": "name",
        "selectedMode": false,
        "itemStyle": {
            "color": "#e8f4f8",
            "borderColor": "#b8d4da",
            "borderWidth": 1
        },
        "emphasis": {}
    }
};
        chart_1c619f32d4c64c7b9b901591100c27a7.setOption(option_1c619f32d4c64c7b9b901591100c27a7);
    </script>
</body>
</html>

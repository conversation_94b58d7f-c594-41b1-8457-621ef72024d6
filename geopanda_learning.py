import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.colors import LinearSegmentedColormap
import json

# --- 1. 数据准备 ---

# 中国地图边界数据URL
china_map_url = "https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json"

# 根据报告整理机场技术难度数据
# 整合空客和C909两个机型的数据，建立综合技术难度评分体系
airport_difficulty_data = {
    '机场代码': [
        'ZWHM', 'ZBDH', 'ZBLA', 'ZSHC', 'ZPLJ', 'ZBHH', 'ZSSS', 'ZBAD', 'ZWSH',
        'ZHCC', 'ZJHK', 'ZSJN', 'ZWFY', 'ZYDQ', 'ZWTL', 'ZWQT', 'ZWYT', 'ZWYN', 'ZYMH'
    ],
    '机场名称': [
        '哈密机场', '大同机场', '拉萨机场', '虹桥机场', '丽江机场', '呼和浩特机场',
        '上海浦东机场', '包头机场', '喀什机场', '郑州机场', '海口机场', '济南机场',
        '富蕴机场', '大庆机场', '吐鲁番机场', '奇台机场', '伊宁机场', '伊宁机场', '漠河机场'
    ],
    '经度': [
        93.67, 113.48, 90.91, 121.34, 100.24, 111.82, 121.81, 110.10, 76.02,
        113.84, 110.46, 117.22, 89.51, 125.14, 89.09, 89.57, 81.33, 81.33, 122.47
    ],
    '纬度': [
        42.84, 40.06, 29.30, 31.20, 26.68, 40.85, 31.14, 40.56, 39.54,
        34.52, 19.93, 36.86, 46.99, 46.98, 43.03, 44.15, 43.96, 43.96, 52.93
    ],
    # 技术难度指标
    '非精密进近次数': [7, 2, 2, 0, 3, 3, 0, 0, 2, 0, 0, 0, 2, 2, 0, 0, 0, 0, 0],
    '高进近次数': [0, 0, 0, 4, 3, 3, 0, 0, 8, 0, 0, 0, 0, 0, 12, 6, 0, 0, 0],
    '平均载荷': [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.365, 1.363, 1.358, 1.0, 1.0, 1.0, 1.356, 1.356, 1.0, 1.0, 1.382, 1.0, 1.0],
    '大侧风乱流次数': [0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 24, 24, 0, 0, 50, 0, 0, 26, 14]
}

# --- 2. 建立综合技术难度评分体系 ---

def calculate_difficulty_score(row):
    """
    计算机场技术难度综合评分
    权重分配：
    - 非精密进近次数：30%（技术要求高）
    - 高进近次数：25%（操作复杂）
    - 平均载荷：20%（超过1.3G认为有难度）
    - 大侧风乱流次数：25%（天气影响大）
    """
    # 标准化处理
    non_precision_score = min(row['非精密进近次数'] / 7 * 100, 100)  # 最高7次
    high_approach_score = min(row['高进近次数'] / 12 * 100, 100)      # 最高12次
    load_score = max((row['平均载荷'] - 1.0) / 0.382 * 100, 0)       # 超过1.0G开始计分
    turbulence_score = min(row['大侧风乱流次数'] / 50 * 100, 100)     # 最高50次
    
    # 加权计算
    total_score = (
        non_precision_score * 0.30 +
        high_approach_score * 0.25 +
        load_score * 0.20 +
        turbulence_score * 0.25
    )
    
    return total_score

# 创建DataFrame
airports_df = pd.DataFrame(airport_difficulty_data)

# 计算综合技术难度评分
airports_df['技术难度评分'] = airports_df.apply(calculate_difficulty_score, axis=1)

# 根据评分进行分级
def get_difficulty_level(score):
    if score >= 80:
        return "极高"
    elif score >= 60:
        return "高"
    elif score >= 40:
        return "中"
    elif score >= 20:
        return "低"
    else:
        return "极低"

airports_df['难度等级'] = airports_df['技术难度评分'].apply(get_difficulty_level)

# 转换为GeoDataFrame
airports_gdf = gpd.GeoDataFrame(
    airports_df,
    geometry=gpd.points_from_xy(airports_df.经度, airports_df.纬度)
)

# --- 3. 绘制热力地图 ---

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

# 创建自定义颜色映射
colors = ['#2E8B57', '#FFD700', '#FF8C00', '#FF4500', '#8B0000']  # 绿-黄-橙-红-深红
n_bins = 100
cmap = LinearSegmentedColormap.from_list('difficulty', colors, N=n_bins)

# 创建画布
fig, ax = plt.subplots(1, 1, figsize=(16, 12))

# 尝试加载中国地图，如果失败则使用简化版本
try:
    china_map = gpd.read_file(china_map_url)
    china_map.plot(ax=ax, color='#f8f8f8', edgecolor='gray', linewidth=0.5)
except:
    print("无法加载在线地图，使用简化版本")
    # 创建一个简化的中国边界框
    ax.set_xlim(70, 140)
    ax.set_ylim(15, 55)
    ax.set_facecolor('#f8f8f8')

# 绘制机场点，大小和颜色代表技术难度
scatter = ax.scatter(
    airports_gdf['经度'], 
    airports_gdf['纬度'],
    c=airports_gdf['技术难度评分'],
    s=airports_gdf['技术难度评分'] * 3 + 50,  # 点的大小与难度成正比
    cmap=cmap,
    alpha=0.8,
    edgecolors='black',
    linewidths=0.5
)

# 添加颜色条
cbar = plt.colorbar(scatter, ax=ax, shrink=0.6)
cbar.set_label('技术难度评分', fontsize=12, fontweight='bold')

# 为高难度机场添加标签
high_difficulty_airports = airports_gdf[airports_gdf['技术难度评分'] >= 40]
for idx, row in high_difficulty_airports.iterrows():
    ax.annotate(
        f"{row['机场名称']}\n({row['技术难度评分']:.1f}分)",
        (row['经度'], row['纬度']),
        xytext=(5, 5),
        textcoords='offset points',
        fontsize=9,
        fontweight='bold' if row['技术难度评分'] >= 80 else 'normal',
        bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8)
    )

# 设置标题和说明
ax.set_title(
    '成都航空机场技术难度热力图\n(2025年6月飞行品质分析)',
    fontsize=18,
    fontweight='bold',
    pad=20
)

# 创建图例
legend_elements = [
    plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#8B0000', 
               markersize=12, label='极高难度 (80+分)'),
    plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#FF4500', 
               markersize=10, label='高难度 (60-79分)'),
    plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#FF8C00', 
               markersize=8, label='中等难度 (40-59分)'),
    plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#FFD700', 
               markersize=6, label='低难度 (20-39分)'),
    plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#2E8B57', 
               markersize=4, label='极低难度 (<20分)')
]
ax.legend(handles=legend_elements, loc='upper left', fontsize=10)

# 移除坐标轴
ax.set_axis_off()

# 调整布局
plt.tight_layout()

# 保存图片
output_filename = 'airport_difficulty_heatmap.png'
plt.savefig(output_filename, dpi=300, bbox_inches='tight')

print(f"机场技术难度热力图已生成: {output_filename}")

# --- 4. 输出详细分析报告 ---

print("\n=== 机场技术难度分析报告 ===")
print(f"总计分析机场数量: {len(airports_df)}")
print("\n各难度等级机场分布:")
difficulty_summary = airports_df['难度等级'].value_counts()
for level, count in difficulty_summary.items():
    print(f"{level}难度: {count}个机场")

print("\n前10大高难度机场:")
top_difficult = airports_df.nlargest(10, '技术难度评分')[['机场名称', '技术难度评分', '难度等级']]
for idx, row in top_difficult.iterrows():
    print(f"{row['机场名称']}: {row['技术难度评分']:.1f}分 ({row['难度等级']})")

print("\n技术难度评分体系说明:")
print("- 非精密进近次数权重: 30%")
print("- 高进近次数权重: 25%") 
print("- 平均载荷权重: 20%")
print("- 大侧风乱流次数权重: 25%")

plt.show()

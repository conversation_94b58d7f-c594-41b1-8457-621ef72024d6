<!DOCTYPE html>
<html>
<head>
    
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css"/>
    
            <meta name="viewport" content="width=device-width,
                initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
            <style>
                #map_457231fc8f6168c1217ed0f28ddbdc79 {
                    position: relative;
                    width: 100.0%;
                    height: 100.0%;
                    left: 0.0%;
                    top: 0.0%;
                }
                .leaflet-container { font-size: 1rem; }
            </style>

            <style>html, body {
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;
            }
            </style>

            <style>#map {
                position:absolute;
                top:0;
                bottom:0;
                right:0;
                left:0;
                }
            </style>

            <script>
                L_NO_TOUCH = false;
                L_DISABLE_3D = false;
            </script>

        
</head>
<body>
    
    
    <div style="position: fixed; 
                top: 10px; right: 10px; width: 200px; height: 180px; 
                background-color: white; border:2px solid grey; z-index:9999; 
                font-size:14px; font-family: 'Microsoft YaHei', sans-serif;
                padding: 10px; box-shadow: 2px 2px 6px rgba(0,0,0,0.3);">
    <h4 style="margin-top: 0; text-align: center; color: #333;">
        空客 风险等级
    </h4>
    <p style="margin: 8px 0;">
        <span style="color: #d73027;">●</span> 高风险 (6+ 分)
    </p>
    <p style="margin: 8px 0;">
        <span style="color: #fc8d59;">●</span> 中高风险 (4-5 分)
    </p>
    <p style="margin: 8px 0;">
        <span style="color: #fee08b;">●</span> 中风险 (2-3 分)
    </p>
    <p style="margin: 8px 0;">
        <span style="color: #91cf60;">●</span> 低风险 (1 分)
    </p>
    <p style="margin: 8px 0; font-size: 12px; color: #666;">
        点击标记查看详情
    </p>
    </div>
    
    
            <div class="folium-map" id="map_457231fc8f6168c1217ed0f28ddbdc79" ></div>
        
</body>
<script>
    
    
            var map_457231fc8f6168c1217ed0f28ddbdc79 = L.map(
                "map_457231fc8f6168c1217ed0f28ddbdc79",
                {
                    center: [35.0, 105.0],
                    crs: L.CRS.EPSG3857,
                    ...{
  "zoom": 5,
  "zoomControl": true,
  "preferCanvas": false,
}

                }
            );

            

        
    
            var tile_layer_b73423ee94dccd088bdde5c656e9635e = L.tileLayer(
                "https://webrd02.is.autonavi.com/appmaptile?lang=zh_cn\u0026size=1\u0026scale=1\u0026style=7\u0026x={x}\u0026y={y}\u0026z={z}",
                {
  "minZoom": 0,
  "maxZoom": 18,
  "maxNativeZoom": 18,
  "noWrap": false,
  "attribution": "\u9ad8\u5fb7\u5730\u56fe",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_b73423ee94dccd088bdde5c656e9635e.addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
            var circle_marker_d416749a4723e420eacf321b72697f73 = L.circleMarker(
                [39.5098, 116.4104],
                {"bubblingMouseEvents": true, "color": "white", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#fee08b", "fillOpacity": 0.8, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 10, "stroke": true, "weight": 2}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
        var popup_52bdec6829a620a1ff9b17a25cbb37e4 = L.popup({
  "maxWidth": 350,
});

        
            
                var html_9b1ce6fbeb0d38b4b6cacfbe63765389 = $(`<div id="html_9b1ce6fbeb0d38b4b6cacfbe63765389" style="width: 100.0%; height: 100.0%;">             <div style="width: 300px; font-family: 'Microsoft YaHei', sans-serif;">                 <h4 style="margin-bottom: 10px; color: #333;">                     🛫 北京大兴 (ZBAD)                 </h4>                 <hr style="margin: 5px 0;">                 <p><strong>📊 总风险分:</strong>                     <span style="color: #fee08b; font-weight: bold; font-size: 16px;">                        2分                    </span>                 </p>                 <p><strong>📋 风险详情:</strong><br>                    平均载荷(2分)                 </p>                 <p><strong>🎯 机型:</strong> 空客</p>                 <p><strong>📍 坐标:</strong> 39.510, 116.410</p>             </div>             </div>`)[0];
                popup_52bdec6829a620a1ff9b17a25cbb37e4.setContent(html_9b1ce6fbeb0d38b4b6cacfbe63765389);
            
        

        circle_marker_d416749a4723e420eacf321b72697f73.bindPopup(popup_52bdec6829a620a1ff9b17a25cbb37e4)
        ;

        
    
    
            circle_marker_d416749a4723e420eacf321b72697f73.bindTooltip(
                `<div>
                     北京大兴 (总分: 2)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var marker_cddf03929d93b5152e8028ca0723f38d = L.marker(
                [39.5098, 116.4104],
                {
}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
            var div_icon_540baf1dafb32b9ce9f1b1ae51d91e4a = L.divIcon({
  "html": "\u003cdiv style=\"font-size: 10px; color: black; font-weight: bold; text-shadow: 1px 1px 1px white; margin-left: 20px; margin-top: -5px;\"\u003e\u5317\u4eac\u5927\u5174\u003c/div\u003e",
  "iconSize": [1, 1],
  "iconAnchor": [0, 0],
  "className": "empty",
});
        
    
                marker_cddf03929d93b5152e8028ca0723f38d.setIcon(div_icon_540baf1dafb32b9ce9f1b1ae51d91e4a);
            
    
            var circle_marker_c80cc9b6f6c652c49819931f0dd2a87a = L.circleMarker(
                [39.6683, 119.0581],
                {"bubblingMouseEvents": true, "color": "white", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#fee08b", "fillOpacity": 0.8, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 10, "stroke": true, "weight": 2}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
        var popup_f58439c5361ece6b5dae3b26ddedbe5d = L.popup({
  "maxWidth": 350,
});

        
            
                var html_44f9e319667a5c50cf9b71e8d33399da = $(`<div id="html_44f9e319667a5c50cf9b71e8d33399da" style="width: 100.0%; height: 100.0%;">             <div style="width: 300px; font-family: 'Microsoft YaHei', sans-serif;">                 <h4 style="margin-bottom: 10px; color: #333;">                     🛫 秦皇岛北戴河 (ZBDH)                 </h4>                 <hr style="margin: 5px 0;">                 <p><strong>📊 总风险分:</strong>                     <span style="color: #fee08b; font-weight: bold; font-size: 16px;">                        2分                    </span>                 </p>                 <p><strong>📋 风险详情:</strong><br>                    非精密进近(2分)                 </p>                 <p><strong>🎯 机型:</strong> 空客</p>                 <p><strong>📍 坐标:</strong> 39.668, 119.058</p>             </div>             </div>`)[0];
                popup_f58439c5361ece6b5dae3b26ddedbe5d.setContent(html_44f9e319667a5c50cf9b71e8d33399da);
            
        

        circle_marker_c80cc9b6f6c652c49819931f0dd2a87a.bindPopup(popup_f58439c5361ece6b5dae3b26ddedbe5d)
        ;

        
    
    
            circle_marker_c80cc9b6f6c652c49819931f0dd2a87a.bindTooltip(
                `<div>
                     秦皇岛北戴河 (总分: 2)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var marker_091aa0e99a302a612fa815a6cd98d932 = L.marker(
                [39.6683, 119.0581],
                {
}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
            var div_icon_af82d6aa04f0305a551fbf7848cf10c6 = L.divIcon({
  "html": "\u003cdiv style=\"font-size: 10px; color: black; font-weight: bold; text-shadow: 1px 1px 1px white; margin-left: 20px; margin-top: -5px;\"\u003e\u79e6\u7687\u5c9b\u5317\u6234\u6cb3\u003c/div\u003e",
  "iconSize": [1, 1],
  "iconAnchor": [0, 0],
  "className": "empty",
});
        
    
                marker_091aa0e99a302a612fa815a6cd98d932.setIcon(div_icon_af82d6aa04f0305a551fbf7848cf10c6);
            
    
            var circle_marker_53dd4909414dcee0b56e00dd4c1aa2a4 = L.circleMarker(
                [40.8517, 111.8242],
                {"bubblingMouseEvents": true, "color": "white", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#91cf60", "fillOpacity": 0.8, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 2}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
        var popup_1486b78847f6054aca67503716059588 = L.popup({
  "maxWidth": 350,
});

        
            
                var html_f394e45445b015cfc5f98725e264dfa7 = $(`<div id="html_f394e45445b015cfc5f98725e264dfa7" style="width: 100.0%; height: 100.0%;">             <div style="width: 300px; font-family: 'Microsoft YaHei', sans-serif;">                 <h4 style="margin-bottom: 10px; color: #333;">                     🛫 呼和浩特 (ZBHH)                 </h4>                 <hr style="margin: 5px 0;">                 <p><strong>📊 总风险分:</strong>                     <span style="color: #91cf60; font-weight: bold; font-size: 16px;">                        1分                    </span>                 </p>                 <p><strong>📋 风险详情:</strong><br>                    高进近(1分)                 </p>                 <p><strong>🎯 机型:</strong> 空客</p>                 <p><strong>📍 坐标:</strong> 40.852, 111.824</p>             </div>             </div>`)[0];
                popup_1486b78847f6054aca67503716059588.setContent(html_f394e45445b015cfc5f98725e264dfa7);
            
        

        circle_marker_53dd4909414dcee0b56e00dd4c1aa2a4.bindPopup(popup_1486b78847f6054aca67503716059588)
        ;

        
    
    
            circle_marker_53dd4909414dcee0b56e00dd4c1aa2a4.bindTooltip(
                `<div>
                     呼和浩特 (总分: 1)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var marker_a4d0baf3a90675723ea8ffbaaf442894 = L.marker(
                [40.8517, 111.8242],
                {
}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
            var div_icon_10ac891e938f0f7a9411ad675cbe9189 = L.divIcon({
  "html": "\u003cdiv style=\"font-size: 10px; color: black; font-weight: bold; text-shadow: 1px 1px 1px white; margin-left: 20px; margin-top: -5px;\"\u003e\u547c\u548c\u6d69\u7279\u003c/div\u003e",
  "iconSize": [1, 1],
  "iconAnchor": [0, 0],
  "className": "empty",
});
        
    
                marker_a4d0baf3a90675723ea8ffbaaf442894.setIcon(div_icon_10ac891e938f0f7a9411ad675cbe9189);
            
    
            var circle_marker_3ccd098665f2bb1b16b6a6ede52dc2fd = L.circleMarker(
                [49.205, 119.825],
                {"bubblingMouseEvents": true, "color": "white", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#91cf60", "fillOpacity": 0.8, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 2}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
        var popup_b025092359b5aa5699438f07164bfebc = L.popup({
  "maxWidth": 350,
});

        
            
                var html_3ad789a463d458d2b4514c6df2531001 = $(`<div id="html_3ad789a463d458d2b4514c6df2531001" style="width: 100.0%; height: 100.0%;">             <div style="width: 300px; font-family: 'Microsoft YaHei', sans-serif;">                 <h4 style="margin-bottom: 10px; color: #333;">                     🛫 呼伦贝尔 (ZBLA)                 </h4>                 <hr style="margin: 5px 0;">                 <p><strong>📊 总风险分:</strong>                     <span style="color: #91cf60; font-weight: bold; font-size: 16px;">                        1分                    </span>                 </p>                 <p><strong>📋 风险详情:</strong><br>                    非精密进近(1分)                 </p>                 <p><strong>🎯 机型:</strong> 空客</p>                 <p><strong>📍 坐标:</strong> 49.205, 119.825</p>             </div>             </div>`)[0];
                popup_b025092359b5aa5699438f07164bfebc.setContent(html_3ad789a463d458d2b4514c6df2531001);
            
        

        circle_marker_3ccd098665f2bb1b16b6a6ede52dc2fd.bindPopup(popup_b025092359b5aa5699438f07164bfebc)
        ;

        
    
    
            circle_marker_3ccd098665f2bb1b16b6a6ede52dc2fd.bindTooltip(
                `<div>
                     呼伦贝尔 (总分: 1)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var marker_91c319c197948f9c375651f5e1268baa = L.marker(
                [49.205, 119.825],
                {
}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
            var div_icon_974504c6822e266254954a1951ccd539 = L.divIcon({
  "html": "\u003cdiv style=\"font-size: 10px; color: black; font-weight: bold; text-shadow: 1px 1px 1px white; margin-left: 20px; margin-top: -5px;\"\u003e\u547c\u4f26\u8d1d\u5c14\u003c/div\u003e",
  "iconSize": [1, 1],
  "iconAnchor": [0, 0],
  "className": "empty",
});
        
    
                marker_91c319c197948f9c375651f5e1268baa.setIcon(div_icon_974504c6822e266254954a1951ccd539);
            
    
            var circle_marker_c04e0697641a3e726ee9817be80f6517 = L.circleMarker(
                [19.9349, 110.4591],
                {"bubblingMouseEvents": true, "color": "white", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#fee08b", "fillOpacity": 0.8, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 10, "stroke": true, "weight": 2}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
        var popup_3ae51716df54cb2c6a00d724f14d2c4d = L.popup({
  "maxWidth": 350,
});

        
            
                var html_6a1c99e86c1e96bba48ff2580242afdd = $(`<div id="html_6a1c99e86c1e96bba48ff2580242afdd" style="width: 100.0%; height: 100.0%;">             <div style="width: 300px; font-family: 'Microsoft YaHei', sans-serif;">                 <h4 style="margin-bottom: 10px; color: #333;">                     🛫 海口美兰 (ZHCC)                 </h4>                 <hr style="margin: 5px 0;">                 <p><strong>📊 总风险分:</strong>                     <span style="color: #fee08b; font-weight: bold; font-size: 16px;">                        3分                    </span>                 </p>                 <p><strong>📋 风险详情:</strong><br>                    大侧风/乱流(3分)                 </p>                 <p><strong>🎯 机型:</strong> 空客</p>                 <p><strong>📍 坐标:</strong> 19.935, 110.459</p>             </div>             </div>`)[0];
                popup_3ae51716df54cb2c6a00d724f14d2c4d.setContent(html_6a1c99e86c1e96bba48ff2580242afdd);
            
        

        circle_marker_c04e0697641a3e726ee9817be80f6517.bindPopup(popup_3ae51716df54cb2c6a00d724f14d2c4d)
        ;

        
    
    
            circle_marker_c04e0697641a3e726ee9817be80f6517.bindTooltip(
                `<div>
                     海口美兰 (总分: 3)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var marker_e1c78eb2e64322230debed480abc0998 = L.marker(
                [19.9349, 110.4591],
                {
}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
            var div_icon_b09c507206d4c0206b112f0c41bdf550 = L.divIcon({
  "html": "\u003cdiv style=\"font-size: 10px; color: black; font-weight: bold; text-shadow: 1px 1px 1px white; margin-left: 20px; margin-top: -5px;\"\u003e\u6d77\u53e3\u7f8e\u5170\u003c/div\u003e",
  "iconSize": [1, 1],
  "iconAnchor": [0, 0],
  "className": "empty",
});
        
    
                marker_e1c78eb2e64322230debed480abc0998.setIcon(div_icon_b09c507206d4c0206b112f0c41bdf550);
            
    
            var circle_marker_c3732ad0c080f7ff1e369a6699f5b01a = L.circleMarker(
                [21.9737, 101.1337],
                {"bubblingMouseEvents": true, "color": "white", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#fee08b", "fillOpacity": 0.8, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 10, "stroke": true, "weight": 2}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
        var popup_e6c71953cd69023dbb7df615398c7d64 = L.popup({
  "maxWidth": 350,
});

        
            
                var html_aaed95a4e5f2d2e0b54d6de6a20fdf46 = $(`<div id="html_aaed95a4e5f2d2e0b54d6de6a20fdf46" style="width: 100.0%; height: 100.0%;">             <div style="width: 300px; font-family: 'Microsoft YaHei', sans-serif;">                 <h4 style="margin-bottom: 10px; color: #333;">                     🛫 西双版纳 (ZJHK)                 </h4>                 <hr style="margin: 5px 0;">                 <p><strong>📊 总风险分:</strong>                     <span style="color: #fee08b; font-weight: bold; font-size: 16px;">                        2分                    </span>                 </p>                 <p><strong>📋 风险详情:</strong><br>                    大侧风/乱流(2分)                 </p>                 <p><strong>🎯 机型:</strong> 空客</p>                 <p><strong>📍 坐标:</strong> 21.974, 101.134</p>             </div>             </div>`)[0];
                popup_e6c71953cd69023dbb7df615398c7d64.setContent(html_aaed95a4e5f2d2e0b54d6de6a20fdf46);
            
        

        circle_marker_c3732ad0c080f7ff1e369a6699f5b01a.bindPopup(popup_e6c71953cd69023dbb7df615398c7d64)
        ;

        
    
    
            circle_marker_c3732ad0c080f7ff1e369a6699f5b01a.bindTooltip(
                `<div>
                     西双版纳 (总分: 2)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var marker_d9c721fc919d036249d67bbaf439e51c = L.marker(
                [21.9737, 101.1337],
                {
}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
            var div_icon_82ce204f6334b98e0e7aa2ba8c1d32a0 = L.divIcon({
  "html": "\u003cdiv style=\"font-size: 10px; color: black; font-weight: bold; text-shadow: 1px 1px 1px white; margin-left: 20px; margin-top: -5px;\"\u003e\u897f\u53cc\u7248\u7eb3\u003c/div\u003e",
  "iconSize": [1, 1],
  "iconAnchor": [0, 0],
  "className": "empty",
});
        
    
                marker_d9c721fc919d036249d67bbaf439e51c.setIcon(div_icon_82ce204f6334b98e0e7aa2ba8c1d32a0);
            
    
            var circle_marker_21d378077922536ad41324221fe3ef14 = L.circleMarker(
                [26.68, 100.2467],
                {"bubblingMouseEvents": true, "color": "white", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#fee08b", "fillOpacity": 0.8, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 10, "stroke": true, "weight": 2}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
        var popup_b3f265ed7bae6b0bc51540d75a124075 = L.popup({
  "maxWidth": 350,
});

        
            
                var html_8df6a732b5d6b0ec88d2e104fce28742 = $(`<div id="html_8df6a732b5d6b0ec88d2e104fce28742" style="width: 100.0%; height: 100.0%;">             <div style="width: 300px; font-family: 'Microsoft YaHei', sans-serif;">                 <h4 style="margin-bottom: 10px; color: #333;">                     🛫 丽江三义 (ZPLJ)                 </h4>                 <hr style="margin: 5px 0;">                 <p><strong>📊 总风险分:</strong>                     <span style="color: #fee08b; font-weight: bold; font-size: 16px;">                        2分                    </span>                 </p>                 <p><strong>📋 风险详情:</strong><br>                    高进近(2分)                 </p>                 <p><strong>🎯 机型:</strong> 空客</p>                 <p><strong>📍 坐标:</strong> 26.680, 100.247</p>             </div>             </div>`)[0];
                popup_b3f265ed7bae6b0bc51540d75a124075.setContent(html_8df6a732b5d6b0ec88d2e104fce28742);
            
        

        circle_marker_21d378077922536ad41324221fe3ef14.bindPopup(popup_b3f265ed7bae6b0bc51540d75a124075)
        ;

        
    
    
            circle_marker_21d378077922536ad41324221fe3ef14.bindTooltip(
                `<div>
                     丽江三义 (总分: 2)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var marker_e621669a9ad2411d67d94c3f63d0c2eb = L.marker(
                [26.68, 100.2467],
                {
}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
            var div_icon_6ea7f572e16201c50137e4a304be7593 = L.divIcon({
  "html": "\u003cdiv style=\"font-size: 10px; color: black; font-weight: bold; text-shadow: 1px 1px 1px white; margin-left: 20px; margin-top: -5px;\"\u003e\u4e3d\u6c5f\u4e09\u4e49\u003c/div\u003e",
  "iconSize": [1, 1],
  "iconAnchor": [0, 0],
  "className": "empty",
});
        
    
                marker_e621669a9ad2411d67d94c3f63d0c2eb.setIcon(div_icon_6ea7f572e16201c50137e4a304be7593);
            
    
            var circle_marker_9685af9171c00bd3cd529f09beca916d = L.circleMarker(
                [30.2295, 120.4348],
                {"bubblingMouseEvents": true, "color": "white", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#fee08b", "fillOpacity": 0.8, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 10, "stroke": true, "weight": 2}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
        var popup_bff1c04da998212ead254e34186f9ef9 = L.popup({
  "maxWidth": 350,
});

        
            
                var html_efa2d76e089206906305d52689bd3e5b = $(`<div id="html_efa2d76e089206906305d52689bd3e5b" style="width: 100.0%; height: 100.0%;">             <div style="width: 300px; font-family: 'Microsoft YaHei', sans-serif;">                 <h4 style="margin-bottom: 10px; color: #333;">                     🛫 杭州萧山 (ZSHC)                 </h4>                 <hr style="margin: 5px 0;">                 <p><strong>📊 总风险分:</strong>                     <span style="color: #fee08b; font-weight: bold; font-size: 16px;">                        3分                    </span>                 </p>                 <p><strong>📋 风险详情:</strong><br>                    高进近(3分)                 </p>                 <p><strong>🎯 机型:</strong> 空客</p>                 <p><strong>📍 坐标:</strong> 30.230, 120.435</p>             </div>             </div>`)[0];
                popup_bff1c04da998212ead254e34186f9ef9.setContent(html_efa2d76e089206906305d52689bd3e5b);
            
        

        circle_marker_9685af9171c00bd3cd529f09beca916d.bindPopup(popup_bff1c04da998212ead254e34186f9ef9)
        ;

        
    
    
            circle_marker_9685af9171c00bd3cd529f09beca916d.bindTooltip(
                `<div>
                     杭州萧山 (总分: 3)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var marker_7d0364ab29e6195a60341dba3371b171 = L.marker(
                [30.2295, 120.4348],
                {
}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
            var div_icon_b6875d88719223acdbc895ff8715d89d = L.divIcon({
  "html": "\u003cdiv style=\"font-size: 10px; color: black; font-weight: bold; text-shadow: 1px 1px 1px white; margin-left: 20px; margin-top: -5px;\"\u003e\u676d\u5dde\u8427\u5c71\u003c/div\u003e",
  "iconSize": [1, 1],
  "iconAnchor": [0, 0],
  "className": "empty",
});
        
    
                marker_7d0364ab29e6195a60341dba3371b171.setIcon(div_icon_b6875d88719223acdbc895ff8715d89d);
            
    
            var circle_marker_d032027d596924f5579fea406a8ad24a = L.circleMarker(
                [36.8572, 117.2156],
                {"bubblingMouseEvents": true, "color": "white", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#91cf60", "fillOpacity": 0.8, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 2}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
        var popup_ff51fb321d3cfb7c6fe4cb6a78d90ed9 = L.popup({
  "maxWidth": 350,
});

        
            
                var html_1939cfdb8387c0d8d435331f1d1f2d7b = $(`<div id="html_1939cfdb8387c0d8d435331f1d1f2d7b" style="width: 100.0%; height: 100.0%;">             <div style="width: 300px; font-family: 'Microsoft YaHei', sans-serif;">                 <h4 style="margin-bottom: 10px; color: #333;">                     🛫 济南遥墙 (ZSJN)                 </h4>                 <hr style="margin: 5px 0;">                 <p><strong>📊 总风险分:</strong>                     <span style="color: #91cf60; font-weight: bold; font-size: 16px;">                        1分                    </span>                 </p>                 <p><strong>📋 风险详情:</strong><br>                    大侧风/乱流(1分)                 </p>                 <p><strong>🎯 机型:</strong> 空客</p>                 <p><strong>📍 坐标:</strong> 36.857, 117.216</p>             </div>             </div>`)[0];
                popup_ff51fb321d3cfb7c6fe4cb6a78d90ed9.setContent(html_1939cfdb8387c0d8d435331f1d1f2d7b);
            
        

        circle_marker_d032027d596924f5579fea406a8ad24a.bindPopup(popup_ff51fb321d3cfb7c6fe4cb6a78d90ed9)
        ;

        
    
    
            circle_marker_d032027d596924f5579fea406a8ad24a.bindTooltip(
                `<div>
                     济南遥墙 (总分: 1)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var marker_2aaa02d99cc3407f37a9314a2525e0da = L.marker(
                [36.8572, 117.2156],
                {
}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
            var div_icon_1cea0aff1fbb1e75fb763a3303f6f075 = L.divIcon({
  "html": "\u003cdiv style=\"font-size: 10px; color: black; font-weight: bold; text-shadow: 1px 1px 1px white; margin-left: 20px; margin-top: -5px;\"\u003e\u6d4e\u5357\u9065\u5899\u003c/div\u003e",
  "iconSize": [1, 1],
  "iconAnchor": [0, 0],
  "className": "empty",
});
        
    
                marker_2aaa02d99cc3407f37a9314a2525e0da.setIcon(div_icon_1cea0aff1fbb1e75fb763a3303f6f075);
            
    
            var circle_marker_11729f90dd242aa458443a7f2686f137 = L.circleMarker(
                [31.1979, 121.3364],
                {"bubblingMouseEvents": true, "color": "white", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#fee08b", "fillOpacity": 0.8, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 10, "stroke": true, "weight": 2}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
        var popup_cdfc3fa4e37e337ebd93542885cb65cf = L.popup({
  "maxWidth": 350,
});

        
            
                var html_0656d1e52bd9400a75c2aeb9ac794f84 = $(`<div id="html_0656d1e52bd9400a75c2aeb9ac794f84" style="width: 100.0%; height: 100.0%;">             <div style="width: 300px; font-family: 'Microsoft YaHei', sans-serif;">                 <h4 style="margin-bottom: 10px; color: #333;">                     🛫 上海虹桥 (ZSSS)                 </h4>                 <hr style="margin: 5px 0;">                 <p><strong>📊 总风险分:</strong>                     <span style="color: #fee08b; font-weight: bold; font-size: 16px;">                        3分                    </span>                 </p>                 <p><strong>📋 风险详情:</strong><br>                    平均载荷(3分)                 </p>                 <p><strong>🎯 机型:</strong> 空客</p>                 <p><strong>📍 坐标:</strong> 31.198, 121.336</p>             </div>             </div>`)[0];
                popup_cdfc3fa4e37e337ebd93542885cb65cf.setContent(html_0656d1e52bd9400a75c2aeb9ac794f84);
            
        

        circle_marker_11729f90dd242aa458443a7f2686f137.bindPopup(popup_cdfc3fa4e37e337ebd93542885cb65cf)
        ;

        
    
    
            circle_marker_11729f90dd242aa458443a7f2686f137.bindTooltip(
                `<div>
                     上海虹桥 (总分: 3)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var marker_6b9ebe168888bd81dbe456a3c129292f = L.marker(
                [31.1979, 121.3364],
                {
}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
            var div_icon_8604e1ea415091504a9c3cb0764b3769 = L.divIcon({
  "html": "\u003cdiv style=\"font-size: 10px; color: black; font-weight: bold; text-shadow: 1px 1px 1px white; margin-left: 20px; margin-top: -5px;\"\u003e\u4e0a\u6d77\u8679\u6865\u003c/div\u003e",
  "iconSize": [1, 1],
  "iconAnchor": [0, 0],
  "className": "empty",
});
        
    
                marker_6b9ebe168888bd81dbe456a3c129292f.setIcon(div_icon_8604e1ea415091504a9c3cb0764b3769);
            
    
            var circle_marker_ae1c4347839fc0eed82bd28817fa9502 = L.circleMarker(
                [30.7837, 114.2081],
                {"bubblingMouseEvents": true, "color": "white", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#fee08b", "fillOpacity": 0.8, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 10, "stroke": true, "weight": 2}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
        var popup_a99876cff38246e96fe624794c87367b = L.popup({
  "maxWidth": 350,
});

        
            
                var html_f3323460f66a7e55ffaaa1e5140998d6 = $(`<div id="html_f3323460f66a7e55ffaaa1e5140998d6" style="width: 100.0%; height: 100.0%;">             <div style="width: 300px; font-family: 'Microsoft YaHei', sans-serif;">                 <h4 style="margin-bottom: 10px; color: #333;">                     🛫 武汉天河 (ZWHM)                 </h4>                 <hr style="margin: 5px 0;">                 <p><strong>📊 总风险分:</strong>                     <span style="color: #fee08b; font-weight: bold; font-size: 16px;">                        3分                    </span>                 </p>                 <p><strong>📋 风险详情:</strong><br>                    非精密进近(3分)                 </p>                 <p><strong>🎯 机型:</strong> 空客</p>                 <p><strong>📍 坐标:</strong> 30.784, 114.208</p>             </div>             </div>`)[0];
                popup_a99876cff38246e96fe624794c87367b.setContent(html_f3323460f66a7e55ffaaa1e5140998d6);
            
        

        circle_marker_ae1c4347839fc0eed82bd28817fa9502.bindPopup(popup_a99876cff38246e96fe624794c87367b)
        ;

        
    
    
            circle_marker_ae1c4347839fc0eed82bd28817fa9502.bindTooltip(
                `<div>
                     武汉天河 (总分: 3)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var marker_b5439ecb08131b7d5109e0af3048db48 = L.marker(
                [30.7837, 114.2081],
                {
}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
            var div_icon_2f5ffb6adc25063e5957b263691e0f2c = L.divIcon({
  "html": "\u003cdiv style=\"font-size: 10px; color: black; font-weight: bold; text-shadow: 1px 1px 1px white; margin-left: 20px; margin-top: -5px;\"\u003e\u6b66\u6c49\u5929\u6cb3\u003c/div\u003e",
  "iconSize": [1, 1],
  "iconAnchor": [0, 0],
  "className": "empty",
});
        
    
                marker_b5439ecb08131b7d5109e0af3048db48.setIcon(div_icon_2f5ffb6adc25063e5957b263691e0f2c);
            
    
            var circle_marker_e9c48911e8bccb8ea0627ff903234b22 = L.circleMarker(
                [39.5428, 76.02],
                {"bubblingMouseEvents": true, "color": "white", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#91cf60", "fillOpacity": 0.8, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 2}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
        var popup_0ddbc517f40ae8d8947e45a2a33fae54 = L.popup({
  "maxWidth": 350,
});

        
            
                var html_7044338f5690cc3743eede35283799b6 = $(`<div id="html_7044338f5690cc3743eede35283799b6" style="width: 100.0%; height: 100.0%;">             <div style="width: 300px; font-family: 'Microsoft YaHei', sans-serif;">                 <h4 style="margin-bottom: 10px; color: #333;">                     🛫 喀什徕宁 (ZWSH)                 </h4>                 <hr style="margin: 5px 0;">                 <p><strong>📊 总风险分:</strong>                     <span style="color: #91cf60; font-weight: bold; font-size: 16px;">                        1分                    </span>                 </p>                 <p><strong>📋 风险详情:</strong><br>                    平均载荷(1分)                 </p>                 <p><strong>🎯 机型:</strong> 空客</p>                 <p><strong>📍 坐标:</strong> 39.543, 76.020</p>             </div>             </div>`)[0];
                popup_0ddbc517f40ae8d8947e45a2a33fae54.setContent(html_7044338f5690cc3743eede35283799b6);
            
        

        circle_marker_e9c48911e8bccb8ea0627ff903234b22.bindPopup(popup_0ddbc517f40ae8d8947e45a2a33fae54)
        ;

        
    
    
            circle_marker_e9c48911e8bccb8ea0627ff903234b22.bindTooltip(
                `<div>
                     喀什徕宁 (总分: 1)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var marker_280eeed30ff6fb581f5f64571c14111d = L.marker(
                [39.5428, 76.02],
                {
}
            ).addTo(map_457231fc8f6168c1217ed0f28ddbdc79);
        
    
            var div_icon_95a18de54ba9e38c44882389117206b4 = L.divIcon({
  "html": "\u003cdiv style=\"font-size: 10px; color: black; font-weight: bold; text-shadow: 1px 1px 1px white; margin-left: 20px; margin-top: -5px;\"\u003e\u5580\u4ec0\u5f95\u5b81\u003c/div\u003e",
  "iconSize": [1, 1],
  "iconAnchor": [0, 0],
  "className": "empty",
});
        
    
                marker_280eeed30ff6fb581f5f64571c14111d.setIcon(div_icon_95a18de54ba9e38c44882389117206b4);
            
</script>
</html>
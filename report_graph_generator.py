import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# --- 1. 数据准备 (已根据您提供的最新表格更正) ---
# 空白处默认为0
data = {
    '事件名': [
        '空中载荷大', '接地点靠前', '抬前轮速率大', '接地点靠后', 
        '2500英尺以下速度大', '下降率大(2000-1000)', '着陆垂直过载大', '起飞坡度大', 
        '初始爬升速度小', '着陆坡度大', '着陆航向偏离', '下降率大(1000-500)', 
        '着陆速度大', '离地俯仰角大', '反推使用晚'
    ],
    'A319': [1, 2, 5, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0],
    'A321': [1, 2, 0, 7, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0],
    'A320': [15, 6, 2, 0, 4, 2, 1, 2, 0, 1, 1, 0, 1, 1, 1]
}

df = pd.DataFrame(data)

# 重新计算总计，并根据总计对事件进行降序排序
df['合计'] = df[['A319', 'A321', 'A320']].sum(axis=1)
# 使用 ascending=True 方便绘图时最大值在顶部
df_sorted = df.sort_values(by='合计', ascending=True)

# --- 2. 绘图设置 ---
# 设置中文字体，确保图表能正确显示中文
# Windows: 'SimHei' or 'Microsoft YaHei'
# macOS: 'Arial Unicode MS' or 'PingFang SC'
# Linux: 'WenQuanYi Micro Hei' or 'Noto Sans CJK SC'
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 定义一个鲜明的彩色调色板
colors = {
    'A320': '#f39c12',  # 橙色 (数量最多，放最前)
    'A319': '#3498db',  # 蓝色
    'A321': '#58D68D'   # 鲜绿色/青色
}

# --- 3. 绘制彩色堆叠柱状图 ---
fig, ax = plt.subplots(figsize=(14, 10))

# 定义机型堆叠顺序
series_cols = ['A320', 'A319', 'A321']
left = np.zeros(len(df_sorted))

# 循环绘制每个机型的堆叠部分
for col in series_cols:
    bars = ax.barh(df_sorted['事件名'], df_sorted[col], left=left, label=col, color=colors[col], edgecolor='white', linewidth=1.5)
    
    # 在每个堆叠块的中心添加数据标签 (只为非零值添加)
    for j, (bar, val) in enumerate(zip(bars, df_sorted[col])):
        if val > 0:
            ax.text(left[j] + val / 2, 
                    bar.get_y() + bar.get_height() / 2,
                    int(val), 
                    ha='center', 
                    va='center', 
                    color='white',
                    fontsize=11, 
                    weight='bold')
    # 更新下一个系列的起始位置
    left += df_sorted[col].values

# 在每个条形图的末端添加总计标签
for i, total in enumerate(df_sorted['合计']):
    if total > 0:
        ax.text(total + 0.2, i, f'总计: {total}', va='center', ha='left', fontsize=11, color='#333333')

# --- 4. 图表美化与调整 ---
ax.set_xlabel('事件发生次数', fontsize=14, labelpad=10)
ax.set_ylabel('事件类型', fontsize=14, labelpad=10)
ax.set_title('空客A320系列机型事件分布', fontsize=20, pad=20, weight='bold')

# 设置Y轴标签字体大小
ax.tick_params(axis='y', labelsize=12)
ax.tick_params(axis='x', labelsize=11)

# 添加图例
ax.legend(title='机型', fontsize=12, title_fontsize=13, loc='lower right')

# 优化坐标轴和网格线
ax.spines['top'].set_visible(False)
ax.spines['right'].set_visible(False)
ax.spines['left'].set_color('gray')
ax.spines['bottom'].set_color('gray')
ax.xaxis.grid(True, linestyle='--', alpha=0.6, which='major')

# 调整X轴范围，为总计标签留出空间
ax.set_xlim(right=df_sorted['合计'].max() * 1.15)

# 确保布局紧凑美观
plt.tight_layout()

# 显示图表
plt.show()

# 如果需要保存图表，可以取消下面这行代码的注释
# fig.savefig('A320系列事件分布_更新数据_彩色堆叠图.png', dpi=300, bbox_inches='tight')

<!DOCTYPE html>
<html>
<head>
    
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css"/>
    
            <meta name="viewport" content="width=device-width,
                initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
            <style>
                #map_92fb219ae092d9b388c3e37905375289 {
                    position: relative;
                    width: 100.0%;
                    height: 100.0%;
                    left: 0.0%;
                    top: 0.0%;
                }
                .leaflet-container { font-size: 1rem; }
            </style>

            <style>html, body {
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;
            }
            </style>

            <style>#map {
                position:absolute;
                top:0;
                bottom:0;
                right:0;
                left:0;
                }
            </style>

            <script>
                L_NO_TOUCH = false;
                L_DISABLE_3D = false;
            </script>

        
</head>
<body>
    
    
    <div style="position: fixed; 
                top: 10px; right: 10px; width: 200px; height: 180px; 
                background-color: white; border:2px solid grey; z-index:9999; 
                font-size:14px; font-family: 'Microsoft YaHei', sans-serif;
                padding: 10px; box-shadow: 2px 2px 6px rgba(0,0,0,0.3);">
    <h4 style="margin-top: 0; text-align: center; color: #333;">
        C909 风险等级
    </h4>
    <p style="margin: 8px 0;">
        <span style="color: #d73027;">●</span> 高风险 (6+ 分)
    </p>
    <p style="margin: 8px 0;">
        <span style="color: #fc8d59;">●</span> 中高风险 (4-5 分)
    </p>
    <p style="margin: 8px 0;">
        <span style="color: #fee08b;">●</span> 中风险 (2-3 分)
    </p>
    <p style="margin: 8px 0;">
        <span style="color: #91cf60;">●</span> 低风险 (1 分)
    </p>
    <p style="margin: 8px 0; font-size: 12px; color: #666;">
        点击标记查看详情
    </p>
    </div>
    
    
            <div class="folium-map" id="map_92fb219ae092d9b388c3e37905375289" ></div>
        
</body>
<script>
    
    
            var map_92fb219ae092d9b388c3e37905375289 = L.map(
                "map_92fb219ae092d9b388c3e37905375289",
                {
                    center: [35.0, 105.0],
                    crs: L.CRS.EPSG3857,
                    ...{
  "zoom": 5,
  "zoomControl": true,
  "preferCanvas": false,
}

                }
            );

            

        
    
            var tile_layer_4ef037ec43f65a05eea6b45df4e074ea = L.tileLayer(
                "https://webrd02.is.autonavi.com/appmaptile?lang=zh_cn\u0026size=1\u0026scale=1\u0026style=7\u0026x={x}\u0026y={y}\u0026z={z}",
                {
  "minZoom": 0,
  "maxZoom": 18,
  "maxNativeZoom": 18,
  "noWrap": false,
  "attribution": "\u9ad8\u5fb7\u5730\u56fe",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_4ef037ec43f65a05eea6b45df4e074ea.addTo(map_92fb219ae092d9b388c3e37905375289);
        
    
            var circle_marker_a80c3ae8a8cf35d4aad64f5d21da183a = L.circleMarker(
                [46.8044, 89.5117],
                {"bubblingMouseEvents": true, "color": "white", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#fc8d59", "fillOpacity": 0.8, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 12, "stroke": true, "weight": 2}
            ).addTo(map_92fb219ae092d9b388c3e37905375289);
        
    
        var popup_7d9fe5e34c34c6526b37029bf40700a8 = L.popup({
  "maxWidth": 350,
});

        
            
                var html_9be6328168d722f1fcbdfb4ed908d79e = $(`<div id="html_9be6328168d722f1fcbdfb4ed908d79e" style="width: 100.0%; height: 100.0%;">             <div style="width: 300px; font-family: 'Microsoft YaHei', sans-serif;">                 <h4 style="margin-bottom: 10px; color: #333;">                     🛫 富蕴可可托海 (ZWFY)                 </h4>                 <hr style="margin: 5px 0;">                 <p><strong>📊 总风险分:</strong>                     <span style="color: #fc8d59; font-weight: bold; font-size: 16px;">                        4分                    </span>                 </p>                 <p><strong>📋 风险详情:</strong><br>                    非精密进近(3分); 平均载荷(1分)                 </p>                 <p><strong>🎯 机型:</strong> C909</p>                 <p><strong>📍 坐标:</strong> 46.804, 89.512</p>             </div>             </div>`)[0];
                popup_7d9fe5e34c34c6526b37029bf40700a8.setContent(html_9be6328168d722f1fcbdfb4ed908d79e);
            
        

        circle_marker_a80c3ae8a8cf35d4aad64f5d21da183a.bindPopup(popup_7d9fe5e34c34c6526b37029bf40700a8)
        ;

        
    
    
            circle_marker_a80c3ae8a8cf35d4aad64f5d21da183a.bindTooltip(
                `<div>
                     富蕴可可托海 (总分: 4)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var marker_b237a766c5669338d91eed4e82b8e335 = L.marker(
                [46.8044, 89.5117],
                {
}
            ).addTo(map_92fb219ae092d9b388c3e37905375289);
        
    
            var div_icon_14a2487065e7351d8722d5809700fdc2 = L.divIcon({
  "html": "\u003cdiv style=\"font-size: 10px; color: black; font-weight: bold; text-shadow: 1px 1px 1px white; margin-left: 20px; margin-top: -5px;\"\u003e\u5bcc\u8574\u53ef\u53ef\u6258\u6d77\u003c/div\u003e",
  "iconSize": [1, 1],
  "iconAnchor": [0, 0],
  "className": "empty",
});
        
    
                marker_b237a766c5669338d91eed4e82b8e335.setIcon(div_icon_14a2487065e7351d8722d5809700fdc2);
            
    
            var circle_marker_0b29e29a10425bd89242d495cadde056 = L.circleMarker(
                [44.0244, 89.3717],
                {"bubblingMouseEvents": true, "color": "white", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#91cf60", "fillOpacity": 0.8, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 2}
            ).addTo(map_92fb219ae092d9b388c3e37905375289);
        
    
        var popup_eb568ea93df0aded814ac49b84673afa = L.popup({
  "maxWidth": 350,
});

        
            
                var html_3ba8a7e556bacc41a7d291d01691398a = $(`<div id="html_3ba8a7e556bacc41a7d291d01691398a" style="width: 100.0%; height: 100.0%;">             <div style="width: 300px; font-family: 'Microsoft YaHei', sans-serif;">                 <h4 style="margin-bottom: 10px; color: #333;">                     🛫 奇台江布拉克 (ZWQT)                 </h4>                 <hr style="margin: 5px 0;">                 <p><strong>📊 总风险分:</strong>                     <span style="color: #91cf60; font-weight: bold; font-size: 16px;">                        1分                    </span>                 </p>                 <p><strong>📋 风险详情:</strong><br>                    高进近(1分)                 </p>                 <p><strong>🎯 机型:</strong> C909</p>                 <p><strong>📍 坐标:</strong> 44.024, 89.372</p>             </div>             </div>`)[0];
                popup_eb568ea93df0aded814ac49b84673afa.setContent(html_3ba8a7e556bacc41a7d291d01691398a);
            
        

        circle_marker_0b29e29a10425bd89242d495cadde056.bindPopup(popup_eb568ea93df0aded814ac49b84673afa)
        ;

        
    
    
            circle_marker_0b29e29a10425bd89242d495cadde056.bindTooltip(
                `<div>
                     奇台江布拉克 (总分: 1)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var marker_603152fe9af2e3d7f102e452fe098475 = L.marker(
                [44.0244, 89.3717],
                {
}
            ).addTo(map_92fb219ae092d9b388c3e37905375289);
        
    
            var div_icon_d79149b8a7484934fa11e310128cb81a = L.divIcon({
  "html": "\u003cdiv style=\"font-size: 10px; color: black; font-weight: bold; text-shadow: 1px 1px 1px white; margin-left: 20px; margin-top: -5px;\"\u003e\u5947\u53f0\u6c5f\u5e03\u62c9\u514b\u003c/div\u003e",
  "iconSize": [1, 1],
  "iconAnchor": [0, 0],
  "className": "empty",
});
        
    
                marker_603152fe9af2e3d7f102e452fe098475.setIcon(div_icon_d79149b8a7484934fa11e310128cb81a);
            
    
            var circle_marker_bec0d2953d2b13442ed4a06a08011fc0 = L.circleMarker(
                [39.5428, 76.02],
                {"bubblingMouseEvents": true, "color": "white", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#fc8d59", "fillOpacity": 0.8, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 12, "stroke": true, "weight": 2}
            ).addTo(map_92fb219ae092d9b388c3e37905375289);
        
    
        var popup_2800eb30b3a80281177b17da0ddc1483 = L.popup({
  "maxWidth": 350,
});

        
            
                var html_389d067fc7112f9af9aac48a8799f8e6 = $(`<div id="html_389d067fc7112f9af9aac48a8799f8e6" style="width: 100.0%; height: 100.0%;">             <div style="width: 300px; font-family: 'Microsoft YaHei', sans-serif;">                 <h4 style="margin-bottom: 10px; color: #333;">                     🛫 喀什徕宁 (ZWSH)                 </h4>                 <hr style="margin: 5px 0;">                 <p><strong>📊 总风险分:</strong>                     <span style="color: #fc8d59; font-weight: bold; font-size: 16px;">                        4分                    </span>                 </p>                 <p><strong>📋 风险详情:</strong><br>                    非精密进近(2分); 高进近(2分)                 </p>                 <p><strong>🎯 机型:</strong> C909</p>                 <p><strong>📍 坐标:</strong> 39.543, 76.020</p>             </div>             </div>`)[0];
                popup_2800eb30b3a80281177b17da0ddc1483.setContent(html_389d067fc7112f9af9aac48a8799f8e6);
            
        

        circle_marker_bec0d2953d2b13442ed4a06a08011fc0.bindPopup(popup_2800eb30b3a80281177b17da0ddc1483)
        ;

        
    
    
            circle_marker_bec0d2953d2b13442ed4a06a08011fc0.bindTooltip(
                `<div>
                     喀什徕宁 (总分: 4)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var marker_8cdb16114ca0269fb5603954b2c59862 = L.marker(
                [39.5428, 76.02],
                {
}
            ).addTo(map_92fb219ae092d9b388c3e37905375289);
        
    
            var div_icon_a165e2996d2ac059f10c3fb2e428526c = L.divIcon({
  "html": "\u003cdiv style=\"font-size: 10px; color: black; font-weight: bold; text-shadow: 1px 1px 1px white; margin-left: 20px; margin-top: -5px;\"\u003e\u5580\u4ec0\u5f95\u5b81\u003c/div\u003e",
  "iconSize": [1, 1],
  "iconAnchor": [0, 0],
  "className": "empty",
});
        
    
                marker_8cdb16114ca0269fb5603954b2c59862.setIcon(div_icon_a165e2996d2ac059f10c3fb2e428526c);
            
    
            var circle_marker_9f970ea12d9a457cc0723d74f0faeefc = L.circleMarker(
                [42.8067, 89.0983],
                {"bubblingMouseEvents": true, "color": "white", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#d73027", "fillOpacity": 0.8, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15, "stroke": true, "weight": 2}
            ).addTo(map_92fb219ae092d9b388c3e37905375289);
        
    
        var popup_3324f6b66522d3080cc00fb43f805038 = L.popup({
  "maxWidth": 350,
});

        
            
                var html_eb955aa47850b6fed0b11c8650a662e2 = $(`<div id="html_eb955aa47850b6fed0b11c8650a662e2" style="width: 100.0%; height: 100.0%;">             <div style="width: 300px; font-family: 'Microsoft YaHei', sans-serif;">                 <h4 style="margin-bottom: 10px; color: #333;">                     🛫 吐鲁番交河 (ZWTL)                 </h4>                 <hr style="margin: 5px 0;">                 <p><strong>📊 总风险分:</strong>                     <span style="color: #d73027; font-weight: bold; font-size: 16px;">                        6分                    </span>                 </p>                 <p><strong>📋 风险详情:</strong><br>                    高进近(3分); 大侧风/乱流(3分)                 </p>                 <p><strong>🎯 机型:</strong> C909</p>                 <p><strong>📍 坐标:</strong> 42.807, 89.098</p>             </div>             </div>`)[0];
                popup_3324f6b66522d3080cc00fb43f805038.setContent(html_eb955aa47850b6fed0b11c8650a662e2);
            
        

        circle_marker_9f970ea12d9a457cc0723d74f0faeefc.bindPopup(popup_3324f6b66522d3080cc00fb43f805038)
        ;

        
    
    
            circle_marker_9f970ea12d9a457cc0723d74f0faeefc.bindTooltip(
                `<div>
                     吐鲁番交河 (总分: 6)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var marker_1039abeddb8e0d5c6ec19c381df34ebc = L.marker(
                [42.8067, 89.0983],
                {
}
            ).addTo(map_92fb219ae092d9b388c3e37905375289);
        
    
            var div_icon_f852a7e1c14967febecbe8fc1cdcdbb0 = L.divIcon({
  "html": "\u003cdiv style=\"font-size: 10px; color: black; font-weight: bold; text-shadow: 1px 1px 1px white; margin-left: 20px; margin-top: -5px;\"\u003e\u5410\u9c81\u756a\u4ea4\u6cb3\u003c/div\u003e",
  "iconSize": [1, 1],
  "iconAnchor": [0, 0],
  "className": "empty",
});
        
    
                marker_1039abeddb8e0d5c6ec19c381df34ebc.setIcon(div_icon_f852a7e1c14967febecbe8fc1cdcdbb0);
            
    
            var circle_marker_5df2c5953aa354ad24383481cf34f0b1 = L.circleMarker(
                [38.1383, 77.7242],
                {"bubblingMouseEvents": true, "color": "white", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#fee08b", "fillOpacity": 0.8, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 10, "stroke": true, "weight": 2}
            ).addTo(map_92fb219ae092d9b388c3e37905375289);
        
    
        var popup_6de04330ff413629f4bd17b21d2a5eec = L.popup({
  "maxWidth": 350,
});

        
            
                var html_d731d3f4ea3000311ec85b8d69b2c299 = $(`<div id="html_d731d3f4ea3000311ec85b8d69b2c299" style="width: 100.0%; height: 100.0%;">             <div style="width: 300px; font-family: 'Microsoft YaHei', sans-serif;">                 <h4 style="margin-bottom: 10px; color: #333;">                     🛫 莎车叶尔羌 (ZWYN)                 </h4>                 <hr style="margin: 5px 0;">                 <p><strong>📊 总风险分:</strong>                     <span style="color: #fee08b; font-weight: bold; font-size: 16px;">                        2分                    </span>                 </p>                 <p><strong>📋 风险详情:</strong><br>                    大侧风/乱流(2分)                 </p>                 <p><strong>🎯 机型:</strong> C909</p>                 <p><strong>📍 坐标:</strong> 38.138, 77.724</p>             </div>             </div>`)[0];
                popup_6de04330ff413629f4bd17b21d2a5eec.setContent(html_d731d3f4ea3000311ec85b8d69b2c299);
            
        

        circle_marker_5df2c5953aa354ad24383481cf34f0b1.bindPopup(popup_6de04330ff413629f4bd17b21d2a5eec)
        ;

        
    
    
            circle_marker_5df2c5953aa354ad24383481cf34f0b1.bindTooltip(
                `<div>
                     莎车叶尔羌 (总分: 2)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var marker_d621bc08039d8080b15b1b77196f4053 = L.marker(
                [38.1383, 77.7242],
                {
}
            ).addTo(map_92fb219ae092d9b388c3e37905375289);
        
    
            var div_icon_be03e2a9f9f34d806c037cfa5549dff1 = L.divIcon({
  "html": "\u003cdiv style=\"font-size: 10px; color: black; font-weight: bold; text-shadow: 1px 1px 1px white; margin-left: 20px; margin-top: -5px;\"\u003e\u838e\u8f66\u53f6\u5c14\u7f8c\u003c/div\u003e",
  "iconSize": [1, 1],
  "iconAnchor": [0, 0],
  "className": "empty",
});
        
    
                marker_d621bc08039d8080b15b1b77196f4053.setIcon(div_icon_be03e2a9f9f34d806c037cfa5549dff1);
            
    
            var circle_marker_6bf91522ef9e5813ba55c483da3a9857 = L.circleMarker(
                [43.9556, 81.3303],
                {"bubblingMouseEvents": true, "color": "white", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#fee08b", "fillOpacity": 0.8, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 10, "stroke": true, "weight": 2}
            ).addTo(map_92fb219ae092d9b388c3e37905375289);
        
    
        var popup_134e74da05c12b0952b41d36e660125a = L.popup({
  "maxWidth": 350,
});

        
            
                var html_e6ade5818b67b99d48438ac735df0e51 = $(`<div id="html_e6ade5818b67b99d48438ac735df0e51" style="width: 100.0%; height: 100.0%;">             <div style="width: 300px; font-family: 'Microsoft YaHei', sans-serif;">                 <h4 style="margin-bottom: 10px; color: #333;">                     🛫 伊宁 (ZWYT)                 </h4>                 <hr style="margin: 5px 0;">                 <p><strong>📊 总风险分:</strong>                     <span style="color: #fee08b; font-weight: bold; font-size: 16px;">                        3分                    </span>                 </p>                 <p><strong>📋 风险详情:</strong><br>                    平均载荷(3分)                 </p>                 <p><strong>🎯 机型:</strong> C909</p>                 <p><strong>📍 坐标:</strong> 43.956, 81.330</p>             </div>             </div>`)[0];
                popup_134e74da05c12b0952b41d36e660125a.setContent(html_e6ade5818b67b99d48438ac735df0e51);
            
        

        circle_marker_6bf91522ef9e5813ba55c483da3a9857.bindPopup(popup_134e74da05c12b0952b41d36e660125a)
        ;

        
    
    
            circle_marker_6bf91522ef9e5813ba55c483da3a9857.bindTooltip(
                `<div>
                     伊宁 (总分: 3)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var marker_f40a86524747c5814cddf9234909850e = L.marker(
                [43.9556, 81.3303],
                {
}
            ).addTo(map_92fb219ae092d9b388c3e37905375289);
        
    
            var div_icon_3effe16e8ec8bf12cf3b11a599cd3502 = L.divIcon({
  "html": "\u003cdiv style=\"font-size: 10px; color: black; font-weight: bold; text-shadow: 1px 1px 1px white; margin-left: 20px; margin-top: -5px;\"\u003e\u4f0a\u5b81\u003c/div\u003e",
  "iconSize": [1, 1],
  "iconAnchor": [0, 0],
  "className": "empty",
});
        
    
                marker_f40a86524747c5814cddf9234909850e.setIcon(div_icon_3effe16e8ec8bf12cf3b11a599cd3502);
            
    
            var circle_marker_bda6ac0653be50971fffc04575bbf2ea = L.circleMarker(
                [46.3733, 124.8758],
                {"bubblingMouseEvents": true, "color": "white", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#fee08b", "fillOpacity": 0.8, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 10, "stroke": true, "weight": 2}
            ).addTo(map_92fb219ae092d9b388c3e37905375289);
        
    
        var popup_51c36ffa2a5ef17fc7d268f5b6805fd2 = L.popup({
  "maxWidth": 350,
});

        
            
                var html_430d20a08f881ff96d1be1adddce8472 = $(`<div id="html_430d20a08f881ff96d1be1adddce8472" style="width: 100.0%; height: 100.0%;">             <div style="width: 300px; font-family: 'Microsoft YaHei', sans-serif;">                 <h4 style="margin-bottom: 10px; color: #333;">                     🛫 大庆萨尔图 (ZYDQ)                 </h4>                 <hr style="margin: 5px 0;">                 <p><strong>📊 总风险分:</strong>                     <span style="color: #fee08b; font-weight: bold; font-size: 16px;">                        3分                    </span>                 </p>                 <p><strong>📋 风险详情:</strong><br>                    非精密进近(1分); 平均载荷(2分)                 </p>                 <p><strong>🎯 机型:</strong> C909</p>                 <p><strong>📍 坐标:</strong> 46.373, 124.876</p>             </div>             </div>`)[0];
                popup_51c36ffa2a5ef17fc7d268f5b6805fd2.setContent(html_430d20a08f881ff96d1be1adddce8472);
            
        

        circle_marker_bda6ac0653be50971fffc04575bbf2ea.bindPopup(popup_51c36ffa2a5ef17fc7d268f5b6805fd2)
        ;

        
    
    
            circle_marker_bda6ac0653be50971fffc04575bbf2ea.bindTooltip(
                `<div>
                     大庆萨尔图 (总分: 3)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var marker_0ae067b32414902779eb0f188dd89dae = L.marker(
                [46.3733, 124.8758],
                {
}
            ).addTo(map_92fb219ae092d9b388c3e37905375289);
        
    
            var div_icon_6388626961948048ebde1571a3abc883 = L.divIcon({
  "html": "\u003cdiv style=\"font-size: 10px; color: black; font-weight: bold; text-shadow: 1px 1px 1px white; margin-left: 20px; margin-top: -5px;\"\u003e\u5927\u5e86\u8428\u5c14\u56fe\u003c/div\u003e",
  "iconSize": [1, 1],
  "iconAnchor": [0, 0],
  "className": "empty",
});
        
    
                marker_0ae067b32414902779eb0f188dd89dae.setIcon(div_icon_6388626961948048ebde1571a3abc883);
            
    
            var circle_marker_6d938c1086b0d99f1056c7131712dcb4 = L.circleMarker(
                [52.9311, 122.4289],
                {"bubblingMouseEvents": true, "color": "white", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "#91cf60", "fillOpacity": 0.8, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 2}
            ).addTo(map_92fb219ae092d9b388c3e37905375289);
        
    
        var popup_0967afa5d9f2da5cbce56eae38e054be = L.popup({
  "maxWidth": 350,
});

        
            
                var html_f10e408a5f2069268cbf78ecea5107d1 = $(`<div id="html_f10e408a5f2069268cbf78ecea5107d1" style="width: 100.0%; height: 100.0%;">             <div style="width: 300px; font-family: 'Microsoft YaHei', sans-serif;">                 <h4 style="margin-bottom: 10px; color: #333;">                     🛫 漠河古莲 (ZYMH)                 </h4>                 <hr style="margin: 5px 0;">                 <p><strong>📊 总风险分:</strong>                     <span style="color: #91cf60; font-weight: bold; font-size: 16px;">                        1分                    </span>                 </p>                 <p><strong>📋 风险详情:</strong><br>                    大侧风/乱流(1分)                 </p>                 <p><strong>🎯 机型:</strong> C909</p>                 <p><strong>📍 坐标:</strong> 52.931, 122.429</p>             </div>             </div>`)[0];
                popup_0967afa5d9f2da5cbce56eae38e054be.setContent(html_f10e408a5f2069268cbf78ecea5107d1);
            
        

        circle_marker_6d938c1086b0d99f1056c7131712dcb4.bindPopup(popup_0967afa5d9f2da5cbce56eae38e054be)
        ;

        
    
    
            circle_marker_6d938c1086b0d99f1056c7131712dcb4.bindTooltip(
                `<div>
                     漠河古莲 (总分: 1)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var marker_5a175737aabcb0fe6c5bf10c373eaab5 = L.marker(
                [52.9311, 122.4289],
                {
}
            ).addTo(map_92fb219ae092d9b388c3e37905375289);
        
    
            var div_icon_b199df7578c206d250f835c125604c53 = L.divIcon({
  "html": "\u003cdiv style=\"font-size: 10px; color: black; font-weight: bold; text-shadow: 1px 1px 1px white; margin-left: 20px; margin-top: -5px;\"\u003e\u6f20\u6cb3\u53e4\u83b2\u003c/div\u003e",
  "iconSize": [1, 1],
  "iconAnchor": [0, 0],
  "className": "empty",
});
        
    
                marker_5a175737aabcb0fe6c5bf10c373eaab5.setIcon(div_icon_b199df7578c206d250f835c125604c53);
            
</script>
</html>
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# --- 字体设置 ---
def setup_chinese_font():
    """设置中文字体"""
    fonts = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial Unicode MS']
    for font in fonts:
        try:
            plt.rcParams['font.sans-serif'] = [font]
            plt.rcParams['axes.unicode_minus'] = False
            print(f"使用字体: {font}")
            break
        except:
            continue

# --- 机场地理坐标数据 ---
airport_coordinates = {
    'ZWHM': {'name': '武汉天河', 'lat': 30.7837, 'lon': 114.2081},
    'ZBDH': {'name': '秦皇岛北戴河', 'lat': 39.6683, 'lon': 119.0581},
    'ZBLA': {'name': '呼伦贝尔', 'lat': 49.2050, 'lon': 119.8250},
    'ZSHC': {'name': '杭州萧山', 'lat': 30.2295, 'lon': 120.4348},
    'ZPLJ': {'name': '丽江三义', 'lat': 26.6800, 'lon': 100.2467},
    'ZBHH': {'name': '呼和浩特', 'lat': 40.8517, 'lon': 111.8242},
    'ZSSS': {'name': '上海虹桥', 'lat': 31.1979, 'lon': 121.3364},
    'ZBAD': {'name': '北京大兴', 'lat': 39.5098, 'lon': 116.4104},
    'ZWSH': {'name': '喀什徕宁', 'lat': 39.5428, 'lon': 76.0200},
    'ZHCC': {'name': '海口美兰', 'lat': 19.9349, 'lon': 110.4591},
    'ZJHK': {'name': '西双版纳', 'lat': 21.9737, 'lon': 101.1337},
    'ZSJN': {'name': '济南遥墙', 'lat': 36.8572, 'lon': 117.2156},
    'ZWFY': {'name': '富蕴可可托海', 'lat': 46.8044, 'lon': 89.5117},
    'ZYDQ': {'name': '大庆萨尔图', 'lat': 46.3733, 'lon': 124.8758},
    'ZWTL': {'name': '吐鲁番交河', 'lat': 42.8067, 'lon': 89.0983},
    'ZWQT': {'name': '奇台江布拉克', 'lat': 44.0244, 'lon': 89.3717},
    'ZWYT': {'name': '伊宁', 'lat': 43.9556, 'lon': 81.3303},
    'ZWYN': {'name': '莎车叶尔羌', 'lat': 38.1383, 'lon': 77.7242},
    'ZYMH': {'name': '漠河古莲', 'lat': 52.9311, 'lon': 122.4289}
}

# --- 原始数据 ---
risk_data = {
    '空客': [
        ('ZWHM', '非精密进近', 1), ('ZBDH', '非精密进近', 2), ('ZBLA', '非精密进近', 3),
        ('ZSHC', '高进近', 1), ('ZPLJ', '高进近', 2), ('ZBHH', '高进近', 3),
        ('ZSSS', '平均载荷', 1), ('ZBAD', '平均载荷', 2), ('ZWSH', '平均载荷', 3),
        ('ZHCC', '大侧风/乱流', 1), ('ZJHK', '大侧风/乱流', 2), ('ZSJN', '大侧风/乱流', 3),
    ],
    'C909': [
        ('ZWFY', '非精密进近', 1), ('ZWSH', '非精密进近', 2), ('ZYDQ', '非精密进近', 3),
        ('ZWTL', '高进近', 1), ('ZWSH', '高进近', 2), ('ZWQT', '高进近', 3),
        ('ZWYT', '平均载荷', 1), ('ZYDQ', '平均载荷', 2), ('ZWFY', '平均载荷', 3),
        ('ZWTL', '大侧风/乱流', 1), ('ZWYN', '大侧风/乱流', 2), ('ZYMH', '大侧风/乱流', 3),
    ]
}

points_map = {1: 3, 2: 2, 3: 1}

def process_airport_data(aircraft_type):
    """处理机场数据，计算风险得分并添加地理坐标"""
    df_data = []
    for code, risk_item, rank in risk_data[aircraft_type]:
        score = points_map[rank]
        airport_info = airport_coordinates.get(code, {})
        airport_name = airport_info.get('name', code)
        lat = airport_info.get('lat', 0)
        lon = airport_info.get('lon', 0)
        
        df_data.append({
            'ICAO': code,
            '机场': airport_name,
            '风险维度': risk_item,
            '风险分': score,
            '纬度': lat,
            '经度': lon
        })
    
    df = pd.DataFrame(df_data)
    
    # 计算每个机场的总风险分
    airport_total_scores = df.groupby(['ICAO', '机场', '纬度', '经度'])['风险分'].sum().reset_index()
    airport_total_scores = airport_total_scores.rename(columns={'风险分': '总风险分'})
    
    # 获取风险详情
    risk_details = df.groupby(['ICAO', '机场']).apply(
        lambda x: '; '.join([f"{row['风险维度']}({row['风险分']}分)" for _, row in x.iterrows()])
    ).reset_index()
    risk_details = risk_details.rename(columns={0: '风险详情'})
    
    # 合并数据
    result = pd.merge(airport_total_scores, risk_details, on=['ICAO', '机场'])
    
    return result

def create_static_risk_map(aircraft_type, save_png=True, figsize=(16, 12)):
    """创建静态风险地图（使用matplotlib）"""
    setup_chinese_font()
    
    # 处理数据
    df = process_airport_data(aircraft_type)
    
    # 创建图形
    fig, ax = plt.subplots(figsize=figsize)
    
    # 设置中国地图的大致边界
    china_bounds = {
        'lon_min': 73, 'lon_max': 135,
        'lat_min': 18, 'lat_max': 54
    }
    
    # 设置地图范围
    ax.set_xlim(china_bounds['lon_min'], china_bounds['lon_max'])
    ax.set_ylim(china_bounds['lat_min'], china_bounds['lat_max'])
    
    # 定义颜色映射（根据风险分数）
    def get_color_and_size(score):
        if score >= 6:
            return '#d73027', 200  # 红色，大圆点
        elif score >= 4:
            return '#fc8d59', 150  # 橙色，中圆点
        elif score >= 2:
            return '#fee08b', 100  # 黄色，中圆点
        else:
            return '#91cf60', 80   # 绿色，小圆点
    
    # 绘制机场标记
    for _, row in df.iterrows():
        if row['纬度'] != 0 and row['经度'] != 0:  # 确保坐标有效
            color, size = get_color_and_size(row['总风险分'])
            
            # 绘制圆点
            ax.scatter(row['经度'], row['纬度'], 
                      c=color, s=size, alpha=0.8, 
                      edgecolors='black', linewidth=1,
                      zorder=5)
            
            # 添加机场名称标签
            ax.annotate(f"{row['机场']}\n({row['总风险分']}分)", 
                       (row['经度'], row['纬度']),
                       xytext=(5, 5), textcoords='offset points',
                       fontsize=8, ha='left', va='bottom',
                       bbox=dict(boxstyle='round,pad=0.3', 
                               facecolor='white', alpha=0.7),
                       zorder=6)
    
    # 设置标题和标签
    ax.set_title(f'{aircraft_type} 机型机场风险分布地图', fontsize=16, pad=20)
    ax.set_xlabel('经度', fontsize=12)
    ax.set_ylabel('纬度', fontsize=12)
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    
    # 创建图例
    legend_elements = [
        plt.scatter([], [], c='#d73027', s=200, alpha=0.8, edgecolors='black', label='高风险 (6+ 分)'),
        plt.scatter([], [], c='#fc8d59', s=150, alpha=0.8, edgecolors='black', label='中高风险 (4-5 分)'),
        plt.scatter([], [], c='#fee08b', s=100, alpha=0.8, edgecolors='black', label='中风险 (2-3 分)'),
        plt.scatter([], [], c='#91cf60', s=80, alpha=0.8, edgecolors='black', label='低风险 (1 分)')
    ]
    
    ax.legend(handles=legend_elements, loc='upper right', 
             bbox_to_anchor=(0.98, 0.98), fontsize=10)
    
    # 设置背景色
    ax.set_facecolor('#f0f8ff')  # 淡蓝色背景
    
    # 调整布局
    plt.tight_layout()
    
    # 保存静态地图
    if save_png:
        filename = f'{aircraft_type}_static_risk_map.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        print(f"静态地图已保存为: {filename}")
    
    plt.show()
    return fig

def main():
    """主程序 - 只生成静态地图"""
    print("🗺️ 静态风险地图生成器启动...")
    
    aircraft_types = ['空客', 'C909']
    
    for aircraft_type in aircraft_types:
        print(f"\n{'='*50}")
        print(f"生成 {aircraft_type} 机型静态风险地图...")
        print(f"{'='*50}")
        
        try:
            create_static_risk_map(aircraft_type)
            print(f"✅ {aircraft_type} 机型静态地图生成完成!")
            
        except Exception as e:
            print(f"❌ 处理 {aircraft_type} 时出错: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🎉 所有静态地图生成完成！")

if __name__ == "__main__":
    main()

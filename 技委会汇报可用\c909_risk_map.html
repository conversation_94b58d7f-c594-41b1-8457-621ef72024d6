<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/maps/china.js"></script>

    
</head>
<body >
    <div id="282dc5499a674664bf922a31ccad0aed" class="chart-container" style="width:100vw; height:100vh; "></div>
    <script>
        var chart_282dc5499a674664bf922a31ccad0aed = echarts.init(
            document.getElementById('282dc5499a674664bf922a31ccad0aed'), 'white', {renderer: 'canvas'});
        var option_282dc5499a674664bf922a31ccad0aed = {
    "backgroundColor": "#f8f9fa",
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "effectScatter",
            "name": "\u673a\u573a\u98ce\u9669\u6307\u6570",
            "coordinateSystem": "geo",
            "showEffectOn": "render",
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            },
            "symbolSize": 15,
            "data": [
                {
                    "name": "\u5bcc\u8574\u53ef\u53ef\u6258\u6d77",
                    "value": [
                        89.516,
                        46.8,
                        4
                    ]
                },
                {
                    "name": "\u5580\u4ec0\u5f95\u5b81",
                    "value": [
                        76.022,
                        39.543,
                        4
                    ]
                },
                {
                    "name": "\u5927\u5e86\u8428\u5c14\u56fe",
                    "value": [
                        124.933,
                        46.75,
                        3
                    ]
                },
                {
                    "name": "\u5410\u9c81\u756a\u4ea4\u6cb3",
                    "value": [
                        89.093,
                        43.03,
                        6
                    ]
                },
                {
                    "name": "\u5947\u53f0\u6c5f\u5e03\u62c9\u514b",
                    "value": [
                        89.6,
                        44.15,
                        1
                    ]
                },
                {
                    "name": "\u4f0a\u5b81",
                    "value": [
                        81.331,
                        43.955,
                        3
                    ]
                },
                {
                    "name": "\u838e\u8f66\u53f6\u5c14\u7f8c",
                    "value": [
                        77.271,
                        38.238,
                        2
                    ]
                },
                {
                    "name": "\u6f20\u6cb3\u53e4\u83b2",
                    "value": [
                        122.4,
                        52.915,
                        1
                    ]
                }
            ],
            "label": {
                "show": true,
                "position": "right",
                "color": "#333",
                "margin": 8,
                "fontSize": 10,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u673a\u573a\u98ce\u9669\u6307\u6570"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "formatter": "{b}<br/>\u98ce\u9669\u6307\u6570: {c}",
        "textStyle": {
            "fontSize": 12
        },
        "backgroundColor": "rgba(255,255,255,0.9)",
        "borderColor": "#34495e",
        "borderWidth": 1,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "C909 \u673a\u578b\u6280\u672f\u96be\u5ea6\u673a\u573a\u5206\u5e03\u56fe",
            "target": "blank",
            "subtext": "\u98ce\u9669\u6307\u6570\u8d8a\u9ad8\uff0c\u5706\u70b9\u8d8a\u5927\u989c\u8272\u8d8a\u6df1\uff0c\u9700\u91cd\u70b9\u5173\u6ce8",
            "subtarget": "blank",
            "left": "center",
            "top": "2%",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false,
            "textStyle": {
                "color": "#2c3e50",
                "fontWeight": "bold",
                "fontSize": 24
            },
            "subtextStyle": {
                "color": "#7f8c8d",
                "fontSize": 14
            }
        }
    ],
    "toolbox": {
        "show": true,
        "orient": "horizontal",
        "itemSize": 15,
        "itemGap": 10,
        "left": "2%",
        "top": "2%",
        "feature": {
            "saveAsImage": {
                "title": "\u4fdd\u5b58\u56fe\u7247"
            },
            "restore": {
                "title": "\u91cd\u7f6e"
            },
            "dataZoom": {
                "title": "\u7f29\u653e"
            }
        }
    },
    "visualMap": {
        "show": true,
        "type": "continuous",
        "min": 1,
        "max": 6,
        "inRange": {
            "color": [
                "#2ecc71",
                "#f39c12",
                "#e74c3c"
            ]
        },
        "calculable": true,
        "inverse": false,
        "splitNumber": 5,
        "hoverLink": true,
        "orient": "vertical",
        "top": "20%",
        "right": "2%",
        "padding": 5,
        "showLabel": true,
        "itemWidth": 20,
        "itemHeight": 140,
        "borderWidth": 0
    },
    "geo": {
        "map": "china",
        "roam": true,
        "aspectScale": 0.75,
        "nameProperty": "name",
        "selectedMode": false,
        "itemStyle": {
            "color": "#e8f4f8",
            "borderColor": "#b8d4da",
            "borderWidth": 1
        },
        "emphasis": {}
    }
};
        chart_282dc5499a674664bf922a31ccad0aed.setOption(option_282dc5499a674664bf922a31ccad0aed);
    </script>
</body>
</html>
